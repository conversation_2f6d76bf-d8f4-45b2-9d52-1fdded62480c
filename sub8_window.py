import sqlite3
from PyQt5.QtWidgets import (<PERSON><PERSON><PERSON><PERSON>, Q<PERSON><PERSON>Layout, QHBoxLayout, QPushButton,
                            QMessageBox, QInputDialog, QFrame, QLabel,
                            QGraphicsDropShadowEffect, QSpacerItem, QSizePolicy,
                            QLineEdit, QGridLayout, QFileDialog, QProgressDialog,
                            QApplication)
from PyQt5.QtGui import QFont, QColor, QIcon
from PyQt5.QtCore import Qt, QDateTime
import os
import shutil
import zipfile
import datetime
import tempfile
import sys

# تم إزالة استيراد مكتبات Access لأنها لم تعد مطلوبة
# البرنامج يستخدم الآن ملفات Excel بدلاً من Access

# تعريف متغيرات للتوافق مع الكود القديم
PYODBC_AVAILABLE = False
PANDAS_ACCESS_AVAILABLE = False
print("تم تعطيل دعم ملفات Access. البرنامج يستخدم الآن ملفات Excel فقط.")

# استيراد pandas كبديل للتعامل مع ملفات Excel
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

# استيراد xlrd و openpyxl للتعامل المباشر مع ملفات Excel
try:
    import xlrd
    XLRD_AVAILABLE = True
except ImportError:
    XLRD_AVAILABLE = False

try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

class Sub8Window(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(" إعدادات البرنامج ")
        # Remove fixed size to allow integration as tab
        self.setFixedSize(900, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setStyleSheet("background-color: #f5f5f5;")  # لون خلفية خفيف

        # تعريف مسار قاعدة البيانات
        self.db_path = "data.db"

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # إنشاء عنوان الصفحة بتصميم أكبر وأكثر وضوحًا
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background-color: #0D47A1;  /* لون أزرق غامق */
                border-radius: 12px;
                min-height: 80px;  /* زيادة ارتفاع الإطار */
            }
        """)
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(20, 15, 20, 15)  # زيادة الهوامش

        # إضافة أيقونة للعنوان بحجم أكبر
        title_label = QLabel("⚙️   إعدادات البرنامج ")
        title_label.setFont(QFont("Amiri", 22, QFont.Bold))  # زيادة حجم الخط
        title_label.setStyleSheet("color: white;")
        title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(title_label)

        # إضافة تأثير الظل للعنوان
        self.apply_shadow(title_frame)

        # إضافة العنوان للتخطيط الرئيسي
        main_layout.addWidget(title_frame)

        # إنشاء إطار للأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 10px;
                border: 1px solid #e0e0e0;
            }
        """)

        # إضافة تأثير الظل للإطار
        self.apply_shadow(buttons_frame)

        # تخطيط الأزرار
        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(20, 20, 20, 20)
        buttons_layout.setSpacing(15)  # تقليل المسافة بين الأزرار

        # تعديل التخطيط ليكون شبكي بدلاً من عمودي
        grid_layout = QGridLayout()
        grid_layout.setHorizontalSpacing(15)
        grid_layout.setVerticalSpacing(15)
        buttons_layout.addLayout(grid_layout)

        # إنشاء الأزرار مع إضافة الزر الجديد
        button_data = [
            ("🗑️ حذف جميع البيانات", "#e74c3c", self.delete_all_data),  # أحمر
            ("🔄 تهيئة البرنامج لبداية سنة دراسية جديدة", "#ff9800", self.reset_school_year),  # برتقالي
            ("🔄 استعادة الإعدادات الافتراضية", "#3498db", self.not_implemented),  # أزرق
            ("🔒 استيراد أرقام الهواتف من النسخة السابقة", "#2ecc71", self.import_phone_numbers),  # أخضر
            ("💾 نسخ احتياطي للبيانات", "#f39c12", self.backup_database),  # برتقالي فاتح - تم تغيير الدالة
            ("📂 استيراد نسخة احتياطية", "#9b59b6", self.restore_backup),  # بنفسجي - تم تحديث الدالة
            ("📊 إحصائيات قاعدة البيانات", "#1abc9c", self.show_libraries_status),  # فيروزي - تم تغيير الدالة
            ("📝 إدراج الغياب الأسبوعي", "#27ae60", self.insert_weekly_absence)  # أخضر غامق
        ]

        # تنظيم الأزرار في صفين (2 عمود و 4 صف)
        self.buttons = []
        for i, (text, color, handler) in enumerate(button_data):
            btn = QPushButton(text)
            btn.setFont(QFont("Amiri", 15, QFont.Bold))  # زيادة حجم الخط وجعله عريضًا
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px;
                    text-align: right;
                    min-height: 40px;
                    min-width: 200px;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                }}
                QPushButton:pressed {{
                    background-color: {self.darken_color(color, factor=30)};
                }}
            """)
            btn.clicked.connect(handler)

            # ترتيب الأزرار في 2 عمود
            row = i // 2
            col = i % 2
            grid_layout.addWidget(btn, row, col)
            self.buttons.append(btn)

        # إضافة مساحة مرنة في نهاية تخطيط الأزرار
        buttons_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # إضافة إطار الأزرار للتخطيط الرئيسي
        main_layout.addWidget(buttons_frame)

    def apply_shadow(self, widget):
        """تطبيق تأثير الظل على العنصر"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(3)
        shadow.setYOffset(3)
        shadow.setColor(QColor(0, 0, 0, 60))
        widget.setGraphicsEffect(shadow)

    def darken_color(self, color, factor=15):
        """تغميق اللون بنسبة معينة"""
        # تحويل اللون من تنسيق hex إلى RGB
        color = color.lstrip('#')
        r, g, b = int(color[0:2], 16), int(color[2:4], 16), int(color[4:6], 16)

        # تقليل قيم RGB بنسبة factor
        r = max(0, r - factor)
        g = max(0, g - factor)
        b = max(0, b - factor)

        # إعادة تحويل اللون إلى تنسيق hex
        return f"#{r:02x}{g:02x}{b:02x}"

    def closeEvent(self, event):
        """منع إغلاق النافذة مباشرة وتوجيه المستخدم لاستخدام زر تسجيل الخروج"""
        try:
            # استيراد الدالة من sub100_window إذا كانت متاحة
            try:
                from sub100_window import CustomDialogs
                CustomDialogs.show_custom_warning_message(
                    self,
                    message="""
                    <div dir='rtl' style='text-align: right;'>
                        <p style='font-family: Calibri; font-size: 14pt; color: #e74c3c; font-weight: bold; margin-bottom: 15px;'>
                            لا يمكن إغلاق النافذة بهذه الطريقة
                        </p>
                        <p style='font-family: Calibri; font-size: 13pt; color: #333333; margin-bottom: 10px;'>
                            الرجاء استخدام زر "تسجيل الخروج" الموجود في الشريط العلوي لإغلاق البرنامج.
                        </p>
                    </div>
                    """,
                    title="تنبيه"
                )
            except ImportError:
                # إذا لم تكن الدالة متاحة، استخدم الطريقة التقليدية
                QMessageBox.warning(
                    self,
                    "تنبيه",
                    "لا يمكن إغلاق النافذة بهذه الطريقة.\nالرجاء استخدام زر 'تسجيل الخروج' الموجود في الشريط العلوي لإغلاق البرنامج."
                )

            # منع إغلاق النافذة
            event.ignore()
        except Exception as e:
            print(f"خطأ في عرض رسالة منع الإغلاق: {e}")
            # في حالة حدوث خطأ، نسمح بإغلاق النافذة
            event.accept()

    def delete_all_data(self):
        """حذف جميع البيانات مع التأكد من كلمة المرور"""
        # إنشاء مربع حوار لإدخال كلمة المرور
        password_dialog = QInputDialog(self)
        password_dialog.setWindowTitle("التحقق من الهوية")
        password_dialog.setLabelText("الرجاء إدخال رمز الحذف للمتابعة:")
        password_dialog.setTextEchoMode(QLineEdit.Password)
        password_dialog.setStyleSheet("""
            QInputDialog {
                background-color: white;
            }
            QLabel {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                color: black;
            }
            QLineEdit {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                font-family: 'Calibri';
                font-size: 13pt;
            }
            QPushButton {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                min-width: 80px;
                padding: 5px;
            }
        """)

        ok = password_dialog.exec_()
        password = password_dialog.textValue()

        # التحقق من صحة كلمة المرور
        if ok and password == "12345":
            # تنفيذ عملية الحذف فورا
            self.perform_deletion()
        else:
            if ok:  # إذا ضغط المستخدم على "موافق" لكن كلمة المرور خاطئة
                self.show_status_message("رمز الحذف غير صحيح!", "error")

    def reset_school_year(self):
        """تهيئة البرنامج لبداية سنة دراسية جديدة من خلال حذف بيانات محددة"""
        # إنشاء مربع حوار للتأكيد
        confirm_dialog = QMessageBox(self)
        confirm_dialog.setWindowTitle("تأكيد التهيئة")
        confirm_dialog.setText("هل أنت متأكد من تهيئة البرنامج لسنة دراسية جديدة؟")
        confirm_dialog.setInformativeText("سيتم حذف البيانات من الجداول التالية:\n- ورقة_السماح_بالدخول\n- تبريرات_الغياب\n- المخالفات")
        confirm_dialog.setIcon(QMessageBox.Warning)
        confirm_dialog.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        confirm_dialog.button(QMessageBox.Yes).setText("نعم")
        confirm_dialog.button(QMessageBox.No).setText("لا")
        confirm_dialog.setStyleSheet("""
            QMessageBox {
                background-color: white;
            }
            QLabel {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                color: black;
            }
            QPushButton {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                min-width: 80px;
                padding: 5px;
            }
        """)

        # تنفيذ التهيئة إذا تم النقر على نعم
        if confirm_dialog.exec_() == QMessageBox.Yes:
            try:
                # إنشاء مربع حوار لإدخال كلمة المرور للتأكيد
                password_dialog = QInputDialog(self)
                password_dialog.setWindowTitle("التحقق من الهوية")
                password_dialog.setLabelText("الرجاء إدخال رمز التأكيد للمتابعة:")
                password_dialog.setTextEchoMode(QLineEdit.Password)
                password_dialog.setStyleSheet("""
                    QInputDialog {
                        background-color: white;
                    }
                    QLabel {
                        font-family: 'Calibri';
                        font-size: 13pt;
                        font-weight: bold;
                        color: black;
                    }
                    QLineEdit {
                        padding: 8px;
                        border: 1px solid #bdc3c7;
                        border-radius: 5px;
                        font-family: 'Calibri';
                        font-size: 13pt;
                    }
                    QPushButton {
                        font-family: 'Calibri';
                        font-size: 13pt;
                        font-weight: bold;
                        min-width: 80px;
                        padding: 5px;
                    }
                """)

                ok = password_dialog.exec_()
                password = password_dialog.textValue()

                # التحقق من صحة كلمة المرور (نفس الرمز المستخدم لحذف جميع البيانات)
                if ok and password == "12345":
                    # تنفيذ عملية التهيئة
                    self.perform_reset()
                else:
                    if ok:  # إذا ضغط المستخدم على "موافق" لكن كلمة المرور خاطئة
                        self.show_status_message("رمز التأكيد غير صحيح!", "error")
            except Exception as e:
                self.show_status_message(f"خطأ أثناء التهيئة: {str(e)}", "error")

    def perform_reset(self):
        """تنفيذ عملية تهيئة البرنامج لبداية سنة دراسية جديدة"""
        try:
            # إنشاء اتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # قائمة الجداول المراد حذف بياناتها
            tables_to_clear = ['ورقة_السماح_بالدخول', 'تبريرات_الغياب', 'المخالفات', 'الشهادة_المدرسية', 'مسك_الغياب_الأسبوعي']
            tables_cleared = []

            # التحقق من وجود كل جدول قبل محاولة حذف البيانات منه
            for table in tables_to_clear:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if cursor.fetchone():
                    cursor.execute(f"DELETE FROM '{table}'")
                    tables_cleared.append(table)

            # إكمال العملية
            conn.commit()
            conn.close()

            # عرض رسالة تأكيد بالجداول التي تم تفريغها
            result_message = "تم تهيئة البرنامج لسنة دراسية جديدة بنجاح!"
            details = "تم حذف البيانات من الجداول التالية:\n"
            for table in tables_cleared:
                details += f"✓ {table}\n"

            self.show_status_message(f"{result_message}\n\n{details}", "success")

        except Exception as e:
            self.show_status_message(f"خطأ أثناء تهيئة البرنامج: {str(e)}", "error")

    def perform_deletion(self):
        """تنفيذ عملية حذف البيانات من الجداول المحددة"""
        try:
            # قائمة الجداول المراد حذف بياناتها
            tables_to_clear = [
                'البنية_التربوية',
                'زيارة_ولي_الأمر',
                'تبريرات_الغياب',
                'المخالفات',
                'الأساتذة',
                'السجل_العام',
                'اللوائح',
                'الشهادة_المدرسية',
                'ورقة_السماح_بالدخول',
                # الجداول الإضافية المطلوبة
                'اخبار_بنشاط',
                'الرمز_السري',
                'السجل_الاولي',
                'زيارات_أولياء_الأمور',
                'غياب_الأسدس_الأول',
                'غياب_الأسدس_الثاني',
                'مجموع_الغياب_السنوي',
                'مسك_الغياب_الأسبوعي'
            ]

            # عرض رسالة تعليمات عادية لتأكيد الحذف
            instruction_dialog = QMessageBox(self)
            instruction_dialog.setWindowTitle("تأكيد الحذف")
            instruction_dialog.setText("تم حذف جميع البيانات بنجاح")

            # إنشاء نص تفصيلي للجداول التي تم حذفها
            deleted_tables_text = "تم مسح البيانات من الجداول التالية:\n"
            for table in tables_to_clear:
                deleted_tables_text += f"• {table}\n"
            deleted_tables_text += "\nمع الاحتفاظ بالسنة الدراسية في جدول بيانات_المؤسسة"
            deleted_tables_text += "\n\nسيتم إغلاق البرنامج الآن لإتمام عملية الحذف وضغط قاعدة البيانات."

            instruction_dialog.setInformativeText(deleted_tables_text)
            instruction_dialog.setIcon(QMessageBox.Information)
            instruction_dialog.setStandardButtons(QMessageBox.Ok)

            # إضافة أيقونة البرنامج إلى نافذة الرسالة
            icon_path = "01.ico"
            if os.path.exists(icon_path):
                instruction_dialog.setWindowIcon(QIcon(icon_path))

            instruction_dialog.setStyleSheet("""
                QMessageBox {
                    background-color: white;
                }
                QLabel {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: black;
                }
                QPushButton {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: white;
                    background-color: #0D47A1;
                    border: none;
                    border-radius: 5px;
                    padding: 5px 15px;
                    min-width: 140px;
                }
                QPushButton:hover {
                    background-color: #1565C0;
                }
                QPushButton:pressed {
                    background-color: #0D47A1;
                }
            """)

            # تنفيذ حذف البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود كل جدول قبل محاولة حذف البيانات منه
            for table in tables_to_clear:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if cursor.fetchone():
                    cursor.execute(f"DELETE FROM {table}")

            # معالجة خاصة لجدول بيانات_المؤسسة - حذف جميع البيانات مع الاحتفاظ بالسنة_الدراسية
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
            if cursor.fetchone():
                # الحصول على السنة الدراسية الحالية
                cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
                result = cursor.fetchone()
                current_school_year = result[0] if result else None

                if current_school_year:
                    # الحصول على أسماء جميع الأعمدة في جدول بيانات_المؤسسة
                    cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
                    columns = [column[1] for column in cursor.fetchall()]

                    # حذف جميع البيانات من الجدول
                    cursor.execute("DELETE FROM بيانات_المؤسسة")

                    # إنشاء استعلام INSERT مع تعيين جميع الأعمدة إلى سلاسل فارغة باستثناء السنة_الدراسية
                    column_names = ", ".join(columns)
                    placeholders = []
                    values = []

                    for column in columns:
                        if column == "السنة_الدراسية":
                            placeholders.append("?")
                            values.append(current_school_year)
                        else:
                            placeholders.append("?")
                            values.append("")  # سلسلة فارغة بدلاً من NULL

                    placeholders_str = ", ".join(placeholders)

                    # إعادة إدخال سجل جديد مع تعيين جميع الأعمدة إلى سلاسل فارغة باستثناء السنة_الدراسية
                    insert_query = f"INSERT INTO بيانات_المؤسسة ({column_names}) VALUES ({placeholders_str})"
                    cursor.execute(insert_query, values)

                    print(f"تم الاحتفاظ بالسنة الدراسية: {current_school_year} وتعيين باقي الحقول إلى سلاسل فارغة")
                else:
                    # إذا لم تكن هناك سنة دراسية، قم بحذف جميع البيانات
                    cursor.execute("DELETE FROM بيانات_المؤسسة")

            # التحقق من وجود جدول اللوائح وإنشائه إذا لم يكن موجوداً
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='اللوائح'")
            if not cursor.fetchone():
                # إنشاء جدول اللوائح إذا لم يكن موجوداً
                try:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS اللوائح (
                            السنة_الدراسية TEXT,
                            القسم TEXT,
                            المستوى TEXT,
                            الرمز TEXT,
                            رت TEXT,
                            مجموع التلاميذ INTEGER DEFAULT 0,
                            PRIMARY KEY(السنة_الدراسية, الرمز)
                        )
                    """)
                    print("تم إنشاء جدول اللوائح")
                except Exception as e:
                    print(f"خطأ في إنشاء جدول اللوائح: {e}")
                    try:
                        cursor.execute("""
                            CREATE TABLE IF NOT EXISTS [اللوائح] (
                                [السنة_الدراسية] TEXT,
                                [القسم] TEXT,
                                [المستوى] TEXT,
                                [الرمز] TEXT,
                                [رت] TEXT,
                                [مجموع التلاميذ] INTEGER DEFAULT 0,
                                PRIMARY KEY([السنة_الدراسية], [الرمز])
                            )
                        """)
                        print("تم إنشاء جدول اللوائح باستخدام الطريقة البديلة")
                    except Exception as e2:
                        print(f"فشل إنشاء جدول اللوائح: {e2}")

            # حذف وإضافة بيانات افتراضية لجدول اللوائح
            try:
                # حذف جميع البيانات من جدول اللوائح
                cursor.execute("DELETE FROM اللوائح")
            except Exception as e:
                print(f"خطأ في حذف بيانات جدول اللوائح: {e}")
                # محاولة بديلة لحذف البيانات
                try:
                    cursor.execute("DELETE FROM [اللوائح]")
                except Exception as e2:
                    print(f"فشلت المحاولة البديلة لحذف بيانات جدول اللوائح: {e2}")

            # إضافة سجل افتراضي لجدول اللوائح
            if current_school_year:
                    # التحقق من وجود جدول السجل_العام
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='السجل_العام'")
                    if cursor.fetchone():
                        # التحقق من وجود السجل في جدول السجل_العام
                        cursor.execute("SELECT COUNT(*) FROM السجل_العام WHERE الرمز = 'A12345678'")
                        if cursor.fetchone()[0] == 0:
                            # إضافة سجل افتراضي في جدول السجل_العام إذا لم يكن موجوداً
                            cursor.execute("""
                                INSERT INTO السجل_العام (الرمز, الاسم_والنسب, السماح, التأخر, عدد_المخالفات, الهاتف_الأول, ملاحظات)
                                VALUES ('A12345678', 'تلميذ افتراضي', '0', '0', '0', '', '')
                            """)
                            print("تم إضافة سجل افتراضي في جدول السجل_العام")
                    else:
                        # إنشاء جدول السجل_العام إذا لم يكن موجوداً
                        cursor.execute("""
                            CREATE TABLE IF NOT EXISTS 'السجل_العام' (
                                'الرمز' TEXT PRIMARY KEY,
                                'الاسم_والنسب' TEXT,
                                'السماح' TEXT,
                                'التأخر' TEXT,
                                'عدد_المخالفات' TEXT,
                                'الهاتف_الأول' TEXT,
                                'ملاحظات' TEXT
                            )
                        """)
                        # إضافة سجل افتراضي في جدول السجل_العام
                        cursor.execute("""
                            INSERT INTO السجل_العام (الرمز, الاسم_والنسب, السماح, التأخر, عدد_المخالفات, الهاتف_الأول, ملاحظات)
                            VALUES ('A12345678', 'تلميذ افتراضي', '0', '0', '0', '', '')
                        """)
                        print("تم إنشاء جدول السجل_العام وإضافة سجل افتراضي")

                    # إضافة سجل افتراضي في جدول اللوائح
                    try:
                        cursor.execute("""
                            INSERT INTO اللوائح (السنة_الدراسية, القسم, المستوى, الرمز, رت, مجموع التلاميذ)
                            VALUES (?, '1APIC-1', 'الأولى إعدادي مسار دولي', 'A12345678', '1', 43)
                        """, (current_school_year,))
                    except Exception as e:
                        print(f"خطأ في إضافة بيانات افتراضية لجدول اللوائح: {e}")
                        # محاولة بديلة لإضافة البيانات
                        try:
                            cursor.execute("""
                                INSERT INTO [اللوائح] (السنة_الدراسية, القسم, المستوى, الرمز, رت, [مجموع التلاميذ])
                                VALUES (?, '1APIC-1', 'الأولى إعدادي مسار دولي', 'A12345678', '1', 43)
                            """, (current_school_year,))
                        except Exception as e2:
                            print(f"فشلت المحاولة البديلة لإضافة بيانات افتراضية لجدول اللوائح: {e2}")
                    print(f"تم إضافة بيانات افتراضية لجدول اللوائح للسنة الدراسية: {current_school_year}")

            # ضغط قاعدة البيانات
            try:
                conn.execute("VACUUM")
                print("تم ضغط قاعدة البيانات بنجاح")
            except Exception as e:
                print(f"خطأ أثناء ضغط قاعدة البيانات: {str(e)}")

            # إكمال العملية وإغلاق الاتصال
            conn.commit()
            conn.close()

            # إضافة دالة للخروج من البرنامج بعد الضغط على زر موافق
            def exit_application():
                print("جاري إغلاق البرنامج...")
                QApplication.quit()

            # ربط زر موافق بدالة الخروج
            instruction_dialog.buttonClicked.connect(exit_application)

            # عرض رسالة التأكيد
            instruction_dialog.exec_()

        except Exception as e:
            self.show_status_message(f"خطأ أثناء حذف البيانات: {str(e)}", "error")

    def show_status_message(self, message, status="info"):
        """عرض رسالة حالة"""
        icon = QMessageBox.Information
        title = "معلومات"

        if status == "error":
            icon = QMessageBox.Critical
            title = "خطأ"
        elif status == "warning":
            icon = QMessageBox.Warning
            title = "تحذير"
        elif status == "success":
            icon = QMessageBox.Information
            title = "نجاح"
        elif status == "progress":
            # في حالة رسائل التقدم، نكتفي بعرض في وحدة التحكم
            print(message)
            return

        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(icon)
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: white;
            }
            QLabel {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                color: black;
            }
            QPushButton {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                min-width: 80px;
                padding: 5px;
            }
        """)
        msg_box.exec_()

    def backup_database(self):
        """عمل نسخة احتياطية لقاعدة البيانات"""
        try:
            # 1. إنشاء مجلد النسخ الاحتياطي على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            backup_folder = os.path.join(main_folder, "النسخ الاحتياطي للبرنامج")

            # إنشاء المجلدات إذا لم تكن موجودة
            if not os.path.exists(main_folder):
                os.makedirs(main_folder)
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)
                self.show_status_message(f"تم إنشاء مجلد النسخ الاحتياطي: {backup_folder}", "info")

            # 2. توليد اسم ملف النسخة الاحتياطية (التاريخ والوقت)
            current_datetime = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            backup_name = f"database_backup_{current_datetime}"
            backup_sqlite = os.path.join(backup_folder, f"{backup_name}.sqlite")
            backup_zip = os.path.join(backup_folder, f"{backup_name}.zip")

            # 3. إصلاح وضغط قاعدة البيانات
            # 3.1 فتح اتصال بقاعدة البيانات الأصلية
            conn = sqlite3.connect(self.db_path)

            # 3.2 إصلاح قاعدة البيانات
            conn.execute("PRAGMA integrity_check")  # التحقق من سلامة قاعدة البيانات
            conn.execute("VACUUM")  # تنظيف وضغط قاعدة البيانات

            # 3.3 إنشاء نسخة احتياطية مؤقتة
            backup_conn = sqlite3.connect(backup_sqlite)
            conn.backup(backup_conn)

            # 3.4 إغلاق الاتصال بقواعد البيانات
            backup_conn.close()
            conn.close()

            # 4. ضغط ملف النسخة الاحتياطية
            with zipfile.ZipFile(backup_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(backup_sqlite, os.path.basename(backup_sqlite))

            # 5. حذف الملف المؤقت بعد إنشاء ملف الضغط
            os.remove(backup_sqlite)

            # 6. حساب حجم النسخة الاحتياطية
            backup_size_kb = os.path.getsize(backup_zip) / 1024
            backup_size_mb = backup_size_kb / 1024

            size_text = f"{backup_size_mb:.2f} MB" if backup_size_mb >= 1 else f"{backup_size_kb:.2f} KB"

            # تم إلغاء فتح مجلد النسخ الاحتياطية تلقائياً

            # إظهار رسالة النجاح مع معلومات إضافية
            success_message = (
                f"تم عمل نسخة احتياطية بنجاح!\n\n"
                f"اسم الملف: {os.path.basename(backup_zip)}\n"
                f"المسار: {backup_folder}\n"
                f"حجم الملف: {size_text}\n"
                f"التاريخ والوقت: {current_datetime.replace('_', ' ')}"
            )

            self.show_status_message(success_message, "success")

        except Exception as e:
            error_message = f"حدث خطأ أثناء عمل نسخة احتياطية:\n{str(e)}"
            self.show_status_message(error_message, "error")

    def restore_backup(self):
        """استيراد نسخة احتياطية من الملفات المضغوطة أو ملفات SQLite مباشرة"""
        try:
            # فتح حوار اختيار الملف في مجلد النسخ الاحتياطية على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            backup_folder = os.path.join(main_folder, "النسخ الاحتياطي للبرنامج")

            # إنشاء المجلدات إذا لم تكن موجودة
            if not os.path.exists(main_folder):
                os.makedirs(main_folder)
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)

            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختر ملف النسخة الاحتياطية",
                backup_folder,
                "جميع ملفات النسخ الاحتياطية (*.zip *.sqlite *.db);;ملفات مضغوطة (*.zip);;ملفات قواعد بيانات (*.sqlite *.db)"
            )

            if not file_path:
                # المستخدم ألغى العملية
                return

            # فحص نوع الملف المختار
            is_zip = file_path.lower().endswith('.zip')
            is_sqlite = file_path.lower().endswith(('.sqlite', '.db'))

            if not (is_zip or is_sqlite):
                self.show_status_message("الملف المختار ليس ملف نسخة احتياطية معتمد. يرجى اختيار ملف بامتداد ZIP أو SQLite.", "error")
                return

            # عرض رسالة تأكيد للمستخدم
            confirm_dialog = QMessageBox(self)
            confirm_dialog.setWindowTitle("تأكيد استعادة النسخة الاحتياطية")
            confirm_dialog.setText("هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟")
            confirm_dialog.setInformativeText("ستتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.")
            confirm_dialog.setIcon(QMessageBox.Warning)
            confirm_dialog.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            confirm_dialog.button(QMessageBox.Yes).setText("نعم")
            confirm_dialog.button(QMessageBox.No).setText("لا")
            confirm_dialog.setStyleSheet("""
                QMessageBox {
                    background-color: white;
                }
                QLabel {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: black;
                }
                QPushButton {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    min-width: 80px;
                    padding: 5px;
                }
            """)

            # التحقق من رغبة المستخدم في المتابعة
            if confirm_dialog.exec_() != QMessageBox.Yes:
                return

            # إنشاء مؤشر تقدم العملية
            progress = QProgressDialog("جاري استعادة النسخة الاحتياطية...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("استعادة النسخة الاحتياطية")
            progress.setWindowModality(Qt.WindowModal)
            progress.setAutoClose(True)
            progress.setMinimumDuration(0)
            progress.setStyleSheet("""
                QProgressDialog {
                    background-color: white;
                }
                QLabel {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: black;
                }
                QProgressBar {
                    border: 1px solid #bdc3c7;
                    border-radius: 3px;
                    text-align: center;
                    background-color: #ecf0f1;
                }
                QProgressBar::chunk {
                    background-color: #9b59b6;
                    width: 10px;
                }
                QPushButton {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    min-width: 80px;
                    padding: 5px;
                }
            """)

            # إظهار مؤشر التقدم
            progress.setValue(0)
            progress.show()

            backup_file_path = None
            temp_dir = None

            # معالجة حسب نوع الملف
            if is_zip:
                # 1. استخراج النسخة الاحتياطية من الملف المضغوط إلى مجلد مؤقت
                progress.setValue(10)
                progress.setLabelText("جاري فحص الملف المضغوط...")

                # التحقق من صحة الملف المضغوط
                try:
                    with zipfile.ZipFile(file_path, 'r') as zip_ref:
                        # التأكد من أن الملف يحتوي على ملف النسخة الاحتياطية
                        file_list = zip_ref.namelist()
                        backup_files = [f for f in file_list if f.endswith(('.sqlite', '.db'))]

                        if not backup_files:
                            self.show_status_message("الملف المختار لا يحتوي على نسخة احتياطية صالحة", "error")
                            progress.close()
                            return

                        # استخراج الملف إلى مجلد مؤقت
                        temp_dir = tempfile.mkdtemp()
                        progress.setValue(30)
                        progress.setLabelText("جاري استخراج النسخة الاحتياطية...")

                        zip_ref.extract(backup_files[0], temp_dir)
                        backup_file_path = os.path.join(temp_dir, backup_files[0])
                except Exception as e:
                    self.show_status_message(f"خطأ في استخراج الملف: {str(e)}", "error")
                    progress.close()
                    return
            else:  # ملف SQLite مباشر
                # في حالة كان الملف قاعدة بيانات مباشرة
                backup_file_path = file_path
                progress.setValue(30)
                progress.setLabelText("تم تحديد ملف قاعدة البيانات...")

            # 2. فحص النسخة الاحتياطية
            progress.setValue(50)
            progress.setLabelText("جاري التحقق من النسخة الاحتياطية...")

            try:
                # التحقق من صحة قاعدة البيانات
                test_conn = sqlite3.connect(backup_file_path)
                test_cursor = test_conn.cursor()

                # التحقق من وجود الجداول الأساسية
                test_cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in test_cursor.fetchall()]

                # الحد الأدنى من الجداول المتوقعة
                essential_tables = ["بيانات_المؤسسة"]

                if not all(table in tables for table in essential_tables):
                    self.show_status_message("النسخة الاحتياطية غير صالحة: لا تحتوي على جميع الجداول الأساسية", "error")
                    test_conn.close()
                    progress.close()
                    # تنظيف المجلد المؤقت
                    if temp_dir:
                        shutil.rmtree(temp_dir, ignore_errors=True)
                    return

                test_conn.close()
            except Exception as e:
                self.show_status_message(f"خطأ في فحص النسخة الاحتياطية: {str(e)}", "error")
                progress.close()
                # تنظيف المجلد المؤقت
                if temp_dir:
                    shutil.rmtree(temp_dir, ignore_errors=True)
                return

            # 3. استبدال قاعدة البيانات الحالية بالنسخة الاحتياطية
            progress.setValue(70)
            progress.setLabelText("جاري استبدال قاعدة البيانات...")

            try:
                # إغلاق أي اتصالات مفتوحة بقاعدة البيانات
                # على المستخدم إغلاق جميع النوافذ المفتوحة قبل الاستعادة

                # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستبدال
                current_time = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
                pre_restore_backup = f"pre_restore_backup_{current_time}.db"
                pre_restore_path = os.path.join(
                    os.path.dirname(os.path.abspath(self.db_path)),
                    "Backups",
                    pre_restore_backup
                )

                # التأكد من وجود مجلد النسخ الاحتياطية
                backup_folder = os.path.join(os.path.dirname(os.path.abspath(self.db_path)), "Backups")
                if not os.path.exists(backup_folder):
                    os.makedirs(backup_folder)

                # نسخ قاعدة البيانات الحالية
                shutil.copy2(self.db_path, pre_restore_path)

                # استبدال قاعدة البيانات الحالية بالنسخة الاحتياطية
                shutil.copy2(backup_file_path, self.db_path)

                progress.setValue(90)
                progress.setLabelText("تم استعادة النسخة الاحتياطية بنجاح!")

                # تنظيف المجلد المؤقت إن وجد
                if temp_dir:
                    shutil.rmtree(temp_dir, ignore_errors=True)

                # إكمال العملية
                progress.setValue(100)

                # عرض رسالة نجاح
                file_name = os.path.basename(file_path)
                success_message = (
                    f"تمت استعادة النسخة الاحتياطية بنجاح!\n\n"
                    f"اسم الملف المستعاد: {file_name}\n"
                    f"تم حفظ نسخة من قاعدة البيانات السابقة في:\n"
                    f"{pre_restore_backup}\n\n"
                    f"يرجى إعادة تشغيل البرنامج لتطبيق التغييرات."
                )
                self.show_status_message(success_message, "success")

                # إغلاق مؤشر التقدم
                progress.close()

            except Exception as e:
                self.show_status_message(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}", "error")
                progress.close()
                # تنظيف المجلد المؤقت
                if temp_dir:
                    shutil.rmtree(temp_dir, ignore_errors=True)
                return

        except Exception as e:
            self.show_status_message(f"حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}", "error")

    def not_implemented(self):
        """رسالة للوظائف غير المطبقة بعد"""
        self.show_status_message("هذه الوظيفة غير متاحة حالياً", "warning")

    def show_libraries_status(self):
        """عرض حالة المكتبات المستخدمة في البرنامج"""
        # إنشاء قائمة بالمكتبات وحالتها
        libraries = [
            {"name": "pandas", "status": PANDAS_AVAILABLE, "description": "للتعامل مع البيانات وملفات Excel (أساسي)"},
            {"name": "xlrd", "status": XLRD_AVAILABLE, "description": "للتعامل مع ملفات Excel القديمة (اختياري)"},
            {"name": "openpyxl", "status": OPENPYXL_AVAILABLE, "description": "للتعامل مع ملفات Excel الحديثة (اختياري)"},
            {"name": "sqlite3", "status": True, "description": "لقاعدة البيانات المحلية (مدمج مع Python)"},
            {"name": "PyQt5", "status": True, "description": "لواجهة المستخدم الرسومية (مثبت بالفعل)"}
        ]

        # إنشاء نص HTML لعرض حالة المكتبات
        html_content = """
        <div style="font-family: 'Calibri'; text-align: right; direction: rtl;">
            <h2 style="color: #1abc9c; text-align: center;">حالة المكتبات المستخدمة في البرنامج</h2>
            <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                <tr style="background-color: #1abc9c; color: white;">
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">اسم المكتبة</th>
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">الحالة</th>
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">الوصف</th>
                </tr>
        """

        # إضافة صفوف الجدول
        for i, lib in enumerate(libraries):
            bg_color = "#f9f9f9" if i % 2 == 0 else "white"
            status_text = "✅ متوفر" if lib["status"] else "❌ غير متوفر"
            status_color = "#2ecc71" if lib["status"] else "#e74c3c"

            html_content += f"""
                <tr style="background-color: {bg_color};">
                    <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">{lib["name"]}</td>
                    <td style="padding: 10px; border: 1px solid #ddd; color: {status_color}; font-weight: bold;">{status_text}</td>
                    <td style="padding: 10px; border: 1px solid #ddd;">{lib["description"]}</td>
                </tr>
            """

        # إضافة معلومات إضافية
        html_content += """
            </table>
            <div style="margin-top: 20px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                <h3 style="color: #3498db;">معلومات النظام:</h3>
                <ul>
        """

        # إضافة معلومات النظام
        html_content += f"<li><strong>إصدار Python:</strong> {sys.version.split()[0]}</li>"
        html_content += f"<li><strong>نظام التشغيل:</strong> {sys.platform}</li>"
        html_content += f"<li><strong>مسار Python:</strong> {sys.executable}</li>"

        # إضافة نصائح للتثبيت
        html_content += """
                </ul>
                <h3 style="color: #3498db; margin-top: 15px;">نصائح للتثبيت:</h3>
                <ul>
                    <li>لتثبيت pandas: <code>pip install pandas</code></li>
                    <li>لتثبيت xlrd: <code>pip install xlrd</code></li>
                    <li>لتثبيت openpyxl: <code>pip install openpyxl</code></li>
                </ul>
                <p style="margin-top: 10px; font-style: italic;">ملاحظة: مكتبة pandas هي الأساسية للتعامل مع ملفات Excel. المكتبات الأخرى اختيارية وتساعد في دعم أنواع مختلفة من ملفات Excel.</p>
            </div>
        </div>
        """

        # عرض النافذة مع المعلومات
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextBrowser, QPushButton
        from PyQt5.QtCore import Qt

        dialog = QDialog(self)
        dialog.setWindowTitle("حالة المكتبات")
        dialog.setMinimumSize(800, 600)
        dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(dialog)

        text_browser = QTextBrowser()
        text_browser.setHtml(html_content)
        layout.addWidget(text_browser)

        close_button = QPushButton("إغلاق")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #1abc9c;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Calibri';
                font-size: 14pt;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #16a085;
            }
        """)
        close_button.clicked.connect(dialog.accept)

        layout.addWidget(close_button, alignment=Qt.AlignCenter)

        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            from PyQt5.QtGui import QIcon
            dialog.setWindowIcon(QIcon(icon_path))

        dialog.exec_()

    def import_excel_data(self, excel_file_path, sheet_name=None):
        """استيراد بيانات من ملف Excel باستخدام pandas"""
        try:
            # التحقق من توفر pandas
            if not PANDAS_AVAILABLE:
                return None, "مكتبة pandas غير متوفرة. الرجاء تثبيتها باستخدام الأمر: pip install pandas"

            # التحقق من وجود الملف
            if not os.path.exists(excel_file_path):
                return None, f"الملف غير موجود: {excel_file_path}"

            # التحقق من امتداد الملف
            if not excel_file_path.lower().endswith(('.xlsx', '.xls')):
                return None, f"الملف ليس ملف Excel صالح: {excel_file_path}"

            try:
                # قراءة أسماء الأوراق في ملف Excel
                xls = pd.ExcelFile(excel_file_path)

                # إذا لم يتم تحديد اسم الورقة، إرجاع قائمة الأوراق
                if not sheet_name:
                    return xls.sheet_names, "تم استرجاع قائمة الأوراق بنجاح"

                # قراءة الورقة المحددة
                df = pd.read_excel(excel_file_path, sheet_name=sheet_name)

                # تحويل البيانات إلى قاموس
                data = df.to_dict('records')

                return data, f"تم استيراد البيانات بنجاح من ورقة {sheet_name}"

            except Exception as e:
                return None, f"خطأ في قراءة ملف Excel: {str(e)}"

        except Exception as e:
            return None, f"خطأ عام في استيراد البيانات: {str(e)}"

    def insert_weekly_absence(self):
        """إدراج الغياب الأسبوعي داخل البرنامج"""
        try:
            # الاتصال بقاعدة البيانات "data.db"
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الحصول على السنة الدراسية من جدول بيانات_المؤسسة
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            if not result:
                self.show_status_message("لم يتم العثور على بيانات في جدول بيانات_المؤسسة", "error")
                return
            السنة = result[0]

            # باستخدام تعبير جدول مشترك (CTE) مع دالة النافذة، نختار 10 سجلات فقط من جدول جدولة_مسك_الغياب
            # بعد تصفية السجلات حسب السنة الدراسية
            # ثم نقوم بعمل Cross Join مع سجلات التلاميذ من جدول اللوائح (بعد التصفية بنفس السنة)
            insert_query = """
            WITH limited_schedule AS (
                SELECT *
                FROM (
                    SELECT j.*, ROW_NUMBER() OVER (ORDER BY j.الشهر) as rn
                    FROM جدولة_مسك_الغياب j
                    WHERE j.السنة_الدراسية = ?
                ) sub
                WHERE rn <= 10
            )
            INSERT OR IGNORE INTO مسك_الغياب_الأسبوعي
                (السنة_الدراسية, ملاحظات,الأسدس, الشهر, "1", "2", "3", "4", "5", بداية_الشهر, رمز_التلميذ)
            SELECT
                ls.السنة_الدراسية,
                '' as "ملاحظات",
                ls.الأسدس,
                ls.الشهر,
                '0' as "1",
                '0' as "2",
                '0' as "3",
                '0' as "4",
                '0' as "5",

                ls.بداية_الشهر,
                l.الرمز
            FROM limited_schedule ls
            CROSS JOIN (SELECT * FROM اللوائح WHERE السنة_الدراسية = ?) l
            """
            # تمرير قيمة السنة لكل من تصفية جدول جدولة_مسك_الغياب وجدول اللوائح
            cursor.execute(insert_query, (السنة, السنة))
            conn.commit()

            # حساب عدد السجلات في جدول مسك_الغياب_الأسبوعي بعد الإدراج
            cursor.execute("SELECT COUNT(*) FROM مسك_الغياب_الأسبوعي")
            count = cursor.fetchone()[0]

            self.show_status_message(f"تم إدراج الغياب الأسبوعي داخل البرنامج بنجاح!\nعدد السجلات الحالي: {count}", "success")
        except Exception as e:
            self.show_status_message(f"حدث خطأ أثناء الإدراج: {e}", "error")
        finally:
            if conn:
                conn.close()

    def import_phone_numbers(self):
        """استيراد أرقام الهواتف من النسخة السابقة (ملف إكسل)"""
        try:
            # 1. التحقق من توفر مكتبة pandas
            if not PANDAS_AVAILABLE:
                self.show_status_message(
                    "مكتبة pandas غير متوفرة. الرجاء تثبيتها باستخدام الأمر:\n\npip install pandas",
                    "error"
                )
                return

            # 2. فتح حوار اختيار ملف إكسل
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختر ملف إكسل للبيانات السابقة",
                "",
                "ملفات إكسل (*.xlsx *.xls);;جميع الملفات (*.*)"
            )

            if not file_path:
                # المستخدم ألغى العملية
                return

            # التحقق من وجود الملف
            if not os.path.exists(file_path):
                self.show_status_message(
                    f"الملف المحدد غير موجود:\n{file_path}",
                    "error"
                )
                return

            # 3. إنشاء مؤشر تقدم العملية
            progress = QProgressDialog("جاري استيراد بيانات الهواتف...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("استيراد أرقام الهواتف")
            progress.setWindowModality(Qt.WindowModal)
            progress.setAutoClose(True)
            progress.setStyleSheet("""
                QProgressDialog {
                    background-color: white;
                }
                QLabel {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: black;
                }
                QProgressBar {
                    border: 1px solid #bdc3c7;
                    border-radius: 3px;
                    text-align: center;
                }
                QProgressBar::chunk {
                    background-color: #2ecc71;
                }
                QPushButton {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    min-width: 80px;
                    padding: 5px;
                }
            """)
            progress.show()
            progress.setValue(10)

            try:
                # طباعة مسار الملف للتشخيص
                print(f"مسار ملف إكسيل: {file_path}")
                progress.setLabelText(f"جاري فحص الملف: {os.path.basename(file_path)}")

                # 4. محاولة قراءة ورقات ملف إكسيل
                try:
                    # قراءة أسماء الورقات في ملف إكسيل
                    import pandas as pd
                    xls = pd.ExcelFile(file_path)
                    sheet_names = xls.sheet_names
                    print(f"الورقات الموجودة في الملف: {sheet_names}")
                except Exception as e:
                    error_msg = f"خطأ في قراءة ورقات ملف إكسيل: {str(e)}"
                    print(error_msg)
                    self.show_status_message(error_msg, "error")
                    progress.close()
                    return

                if not sheet_names:
                    self.show_status_message(
                        "لم يتم العثور على أي ورقات في ملف إكسيل المحدد.",
                        "error"
                    )
                    progress.close()
                    return

                # إذا كان هناك أكثر من ورقة، اسأل المستخدم عن الورقة المطلوبة
                target_sheet = None
                if len(sheet_names) > 1:
                    # البحث عن ورقة قد تحتوي على بيانات الطلاب
                    for sheet in sheet_names:
                        if 'سجل' in sheet or 'طلاب' in sheet or 'تلاميذ' in sheet:
                            target_sheet = sheet
                            break

                    # إذا لم نجد ورقة مناسبة، نسأل المستخدم عن الورقة المطلوبة
                    if not target_sheet:
                        from PyQt5.QtWidgets import QInputDialog
                        target_sheet, ok = QInputDialog.getItem(
                            self,
                            "اختيار ورقة البيانات",
                            "اختر الورقة التي تحتوي على بيانات الطلاب:",
                            sheet_names,
                            0,
                            False
                        )
                        if not ok or not target_sheet:
                            self.show_status_message("لم يتم اختيار ورقة بيانات. تم إلغاء العملية.", "info")
                            progress.close()
                            return
                else:
                    # إذا كانت هناك ورقة واحدة فقط، استخدمها
                    target_sheet = sheet_names[0]

                progress.setValue(30)
                progress.setLabelText(f"جاري قراءة البيانات من ورقة {target_sheet}...")

                # 5. قراءة ورقة البيانات مباشرة إلى DataFrame باستخدام pandas
                try:
                    # تعيين نوع البيانات للأعمدة المستهدفة كنصوص لضمان عدم فقدان البيانات
                    dtype_dict = {'الهاتف_الأول': str, 'الهاتف_الثاني': str, 'رقم الهاتف الأول': str, 'رقم الهاتف الثاني': str}

                    # محاولة قراءة الملف مع تحديد نوع البيانات
                    df = pd.read_excel(
                        file_path,
                        sheet_name=target_sheet,
                        dtype=dtype_dict,  # تحديد أنواع البيانات للأعمدة الأساسية
                        keep_default_na=False,  # عدم تحويل القيم الفارغة إلى NaN
                        na_values=["#N/A", "N/A", "NA"]  # تحديد قيم NA المعترف بها
                    )

                    print(f"تم قراءة {len(df)} سجل من الورقة {target_sheet}")
                    print(f"أسماء الأعمدة: {df.columns.tolist()}")
                except Exception as e:
                    error_msg = f"خطأ في قراءة ورقة {target_sheet}: {str(e)}"
                    print(error_msg)
                    self.show_status_message(error_msg, "error")
                    progress.close()
                    return

                # 6. تحديد الأعمدة المطلوبة
                progress.setValue(50)
                progress.setLabelText("جاري تحليل بيانات الجدول...")

                # قاموس للتسميات المختلفة المحتملة للأعمدة
                possible_column_names = {
                    'الرمز': ['الرمز', 'رمز', 'رمز التلميذ', 'رمز الطالب', 'رقم التسجيل', 'id', 'code'],
                    'الهاتف_الأول': ['الهاتف_الأول', 'الهاتف الأول', 'رقم الهاتف الأول', 'الهاتف 1', 'phone1', 'هاتف1', 'هاتف 1', 'هاتف أول'],
                    'الهاتف_الثاني': ['الهاتف_الثاني', 'الهاتف الثاني', 'رقم الهاتف الثاني', 'الهاتف 2', 'phone2', 'هاتف2', 'هاتف 2', 'هاتف ثاني'],
                    'ملاحظات': ['ملاحظات', 'notes', 'ملاحظة', 'notes'],
                    'الرمز_السري': ['الرمز_السري', 'الرمز السري', 'كلمة المرور', 'password']
                }

                # البحث عن الأعمدة باستخدام القاموس
                columns_map = {}
                for req_col, possible_names in possible_column_names.items():
                    for col in df.columns:
                        col_str = str(col).lower().strip()
                        if any(name.lower() in col_str for name in possible_names):
                            columns_map[req_col] = col
                            print(f"تم مطابقة العمود {req_col} مع {col}")
                            break

                # التحقق من وجود عمود الرمز على الأقل
                if 'الرمز' not in columns_map:
                    # عرض أسماء الأعمدة الموجودة وأطلب من المستخدم تحديد عمود الرمز
                    from PyQt5.QtWidgets import QInputDialog
                    code_column, ok = QInputDialog.getItem(
                        self,
                        "تحديد عمود الرمز",
                        "اختر العمود الذي يحتوي على رمز الطلاب:",
                        [str(col) for col in df.columns],
                        0,
                        False
                    )
                    if ok and code_column:
                        columns_map['الرمز'] = code_column
                    else:
                        self.show_status_message("لم يتم تحديد عمود الرمز. تم إلغاء العملية.", "info")
                        progress.close()
                        return

                # 7. معالجة البيانات واستيراد من إكسيل إلى قاعدة البيانات SQLite المحلية
                # الاتصال بقاعدة بيانات SQLite المحلية
                conn_sqlite = sqlite3.connect(self.db_path)
                cursor_sqlite = conn_sqlite.cursor()

                # الحصول على قائمة بالتلاميذ الموجودين في قاعدة البيانات الحالية
                cursor_sqlite.execute("SELECT الرمز FROM السجل_العام")
                existing_students = {row[0] for row in cursor_sqlite.fetchall()}
                print(f"عدد التلاميذ الموجودين في قاعدة البيانات الحالية: {len(existing_students)}")

                # معالجة البيانات وتحديث قاعدة البيانات
                records_updated = 0
                records_failed = 0
                records_not_found = 0

                # إعداد المعاملة
                conn_sqlite.execute('BEGIN TRANSACTION')

                # أسماء الأعمدة في DataFrame
                code_col = columns_map.get('الرمز')
                phone1_col = columns_map.get('الهاتف_الأول', '')
                phone2_col = columns_map.get('الهاتف_الثاني', '')
                notes_col = columns_map.get('ملاحظات', '')
                secret_col = columns_map.get('الرمز_السري', '')

                progress.setValue(60)
                progress.setLabelText("جاري تحديث بيانات الهواتف...")

                # معالجة كل صف في DataFrame
                total_rows = len(df)
                for i, row in df.iterrows():
                    if progress.wasCanceled():
                        conn_sqlite.rollback()
                        conn_sqlite.close()
                        progress.close()
                        return

                    try:
                        # الحصول على قيمة الرمز
                        code = str(row[code_col]).strip() if pd.notna(row[code_col]) else None

                        # تجاهل السجلات بدون رمز
                        if code is None or code == '':
                            continue

                        # تنظيف الرمز من أي مسافات أو أحرف غريبة
                        code = code.strip()

                        # التحقق من وجود الرمز في قاعدة البيانات الحالية
                        if code in existing_students:
                            # الحصول على البيانات وتأكد من معالجتها كنصوص
                            # استخدم str() لضمان التحويل إلى نص وتأكد من عدم وجود قيم NaN

                            # الهاتف الأول
                            if phone1_col and pd.notna(row.get(phone1_col, '')):
                                phone1 = str(row[phone1_col])
                                # تنظيف البيانات من القيم غير المرغوبة
                                phone1 = phone1.strip().replace('nan', '').replace('None', '')
                            else:
                                phone1 = ""

                            # الهاتف الثاني
                            if phone2_col and pd.notna(row.get(phone2_col, '')):
                                phone2 = str(row[phone2_col])
                                # تنظيف البيانات من القيم غير المرغوبة
                                phone2 = phone2.strip().replace('nan', '').replace('None', '')
                            else:
                                phone2 = ""

                            # ملاحظات
                            notes = str(row[notes_col]).strip() if notes_col and pd.notna(row.get(notes_col, '')) else ""

                            # الرمز السري
                            secret = str(row[secret_col]).strip() if secret_col and pd.notna(row.get(secret_col, '')) else ""

                            # تحديث البيانات
                            cursor_sqlite.execute("""
                                UPDATE السجل_العام
                                SET الهاتف_الأول = ?, الهاتف_الثاني = ?, ملاحظات = ?, الرمز_السري = ?
                                WHERE الرمز = ?
                            """, (phone1, phone2, notes, secret, code))

                            records_updated += 1

                            # طباعة تفاصيل التحديث للمساعدة في التشخيص
                            if i % 50 == 0:  # طباعة كل 50 سجل فقط لتجنب الكثير من المخرجات
                                print(f"تم تحديث السجل {i}: الرمز={code}, الهاتف الأول={phone1}, الهاتف الثاني={phone2}")
                        else:
                            records_not_found += 1
                            if records_not_found <= 5:  # طباعة أول 5 سجلات غير موجودة فقط
                                print(f"الرمز غير موجود في قاعدة البيانات: {code}")
                    except Exception as e:
                        records_failed += 1
                        print(f"خطأ في تحديث السجل رقم {i} (الرمز: {code}): {str(e)}")

                    # تحديث مؤشر التقدم
                    progress_value = 60 + int((i + 1) / total_rows * 30)
                    progress.setValue(progress_value)
                    if i % 10 == 0:  # تحديث النص كل 10 سجلات فقط لتحسين الأداء
                        progress.setLabelText(f"جاري تحديث بيانات الهواتف... ({i+1}/{total_rows})")

                # حفظ التغييرات
                conn_sqlite.commit()
                conn_sqlite.close()

                progress.setValue(95)

                # عرض تقرير النجاح
                success_message = (
                    f"تم استيراد أرقام الهواتف والبيانات من ملف إكسيل بنجاح!\n\n"
                    f"اسم الورقة: {target_sheet}\n"
                    f"إجمالي السجلات: {total_rows}\n"
                    f"السجلات المحدثة: {records_updated}\n"
                    f"السجلات غير الموجودة: {records_not_found}\n"
                    f"السجلات الفاشلة: {records_failed}"
                )

                progress.setValue(100)
                self.show_status_message(success_message, "success")

            except Exception as e:
                error_details = str(e)
                print(f"خطأ في استيراد البيانات: {error_details}")
                self.show_status_message(f"خطأ في استيراد البيانات: {error_details}", "error")
            finally:
                if progress and progress.isVisible():
                    progress.close()

        except Exception as e:
            error_details = str(e)
            print(f"حدث خطأ أثناء استيراد أرقام الهواتف: {error_details}")
            self.show_status_message(f"حدث خطأ أثناء استيراد أرقام الهواتف: {error_details}", "error")

if __name__ == '__main__':
    import sys
    from PyQt5.QtWidgets import QApplication
    app = QApplication(sys.argv)
    window = Sub8Window()
    window.show()
    sys.exit(app.exec_())


