"""
نافذة بيانات المؤسسة - Python + HTML
تم إعادة تصميم النافذة لتستخدم منهجية Python + HTML الحديثة

الميزات:
- واجهة HTML جميلة ومتجاوبة
- تكامل كامل مع قاعدة البيانات
- إدارة الشعار والبيانات
- تصميم عصري ومرن
"""

import sys
import os
import json
import sqlite3
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QFileDialog, QMessageBox
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, QTimer
from PyQt5.QtGui import QIcon
import base64


class InstitutionEngine(QObject):
    """محرك إدارة بيانات المؤسسة"""

    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    dataUpdated = pyqtSignal(str)  # institution data JSON
    logoUpdated = pyqtSignal(str)  # logo base64 or path

    def __init__(self):
        super().__init__()
        self.db_path = "data.db"
        self.setup_database()

    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.logUpdated.emit(message, status, timestamp)

    def setup_database(self):
        """إعداد قاعدة البيانات وإنشاء الجداول المطلوبة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إنشاء جدول بيانات المؤسسة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS بيانات_المؤسسة (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    الأكاديمية TEXT,
                    المديرية TEXT,
                    الجماعة TEXT,
                    المؤسسة TEXT,
                    السنة_الدراسية TEXT,
                    البلدة TEXT,
                    المدير TEXT,
                    الحارس_العام TEXT,
                    السلك TEXT,
                    رقم_الحراسة TEXT,
                    رقم_التسجيل TEXT,
                    الأسدس TEXT,
                    الشعار TEXT,
                    تاريخ_التحديث TEXT
                )
            ''')
            
            # إنشاء جدول البنية التربوية إذا لم يكن موجوداً
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS البنية_التربوية (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    السنة_الدراسية TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            self.emit_log("✅ تم إعداد قاعدة البيانات بنجاح", "success")
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في إعداد قاعدة البيانات: {str(e)}", "error")

    @pyqtSlot(result=str)
    def getInstitutionData(self):
        """الحصول على بيانات المؤسسة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # الحصول على بيانات المؤسسة
            cursor.execute("SELECT * FROM بيانات_المؤسسة LIMIT 1")
            row = cursor.fetchone()
            
            if row:
                # الحصول على أسماء الأعمدة
                cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
                columns = [info[1] for info in cursor.fetchall()]
                
                # تحويل البيانات إلى قاموس
                data = dict(zip(columns, row))
            else:
                # بيانات افتراضية
                data = {
                    "الأكاديمية": "",
                    "المديرية": "",
                    "الجماعة": "",
                    "المؤسسة": "",
                    "السنة_الدراسية": "",
                    "البلدة": "",
                    "المدير": "من مدير",
                    "الحارس_العام": "من حارس عام",
                    "السلك": "التعليم الابتدائي",
                    "رقم_الحراسة": "حراسة رقم 1",
                    "رقم_التسجيل": "",
                    "الأسدس": "الأول",
                    "الشعار": ""
                }
            
            conn.close()
            return json.dumps(data, ensure_ascii=False)
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في جلب بيانات المؤسسة: {str(e)}", "error")
            return json.dumps({"error": str(e)}, ensure_ascii=False)

    @pyqtSlot(result=str)
    def getAcademicYears(self):
        """الحصول على السنوات الدراسية"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # جلب السنوات الدراسية من جدول البنية التربوية
            cursor.execute("SELECT DISTINCT السنة_الدراسية FROM البنية_التربوية ORDER BY السنة_الدراسية DESC")
            years = [row[0] for row in cursor.fetchall() if row[0]]
            
            # إذا لم توجد سنوات، جلب من جدول اللوائح
            if not years:
                cursor.execute("SELECT DISTINCT السنة_الدراسية FROM اللوائح ORDER BY السنة_الدراسية DESC")
                years = [row[0] for row in cursor.fetchall() if row[0]]
            
            # إضافة سنوات افتراضية إذا لم توجد
            if not years:
                years = ["2024-2025", "2023-2024", "2022-2023"]
            
            conn.close()
            return json.dumps(years, ensure_ascii=False)            
        except Exception as e:
            self.emit_log(f"❌ خطأ في جلب السنوات الدراسية: {str(e)}", "error")
            return json.dumps([], ensure_ascii=False)
    
    @pyqtSlot(str)
    def saveInstitutionData(self, data_json):
        """حفظ بيانات المؤسسة"""
        try:
            data = json.loads(data_json)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود بيانات
            cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
            count = cursor.fetchone()[0]
            
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            if count > 0:
                # تحديث البيانات الموجودة
                cursor.execute('''
                    UPDATE بيانات_المؤسسة SET
                    الأكاديمية = ?, المديرية = ?, الجماعة = ?, المؤسسة = ?,
                    السنة_الدراسية = ?, البلدة = ?, المدير = ?, الحارس_العام = ?,
                    السلك = ?, رقم_الحراسة = ?, رقم_التسجيل = ?, الأسدس = ?,
                    تاريخ_التحديث = ?
                    WHERE id = 1
                ''', (
                    data.get("الأكاديمية", ""),
                    data.get("المديرية", ""),
                    data.get("الجماعة", ""),
                    data.get("المؤسسة", ""),
                    data.get("السنة_الدراسية", ""),
                    data.get("البلدة", ""),
                    data.get("المدير", "من مدير"),
                    data.get("الحارس_العام", "من حارس عام"),
                    data.get("السلك", "التعليم الابتدائي"),
                    data.get("رقم_الحراسة", "حراسة رقم 1"),
                    data.get("رقم_التسجيل", ""),
                    data.get("الأسدس", "الأول"),
                    current_time
                ))
            else:
                # إدراج بيانات جديدة
                cursor.execute('''
                    INSERT INTO بيانات_المؤسسة (
                        الأكاديمية, المديرية, الجماعة, المؤسسة,
                        السنة_الدراسية, البلدة, المدير, الحارس_العام,
                        السلك, رقم_الحراسة, رقم_التسجيل, الأسدس,
                        تاريخ_التحديث
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    data.get("الأكاديمية", ""),
                    data.get("المديرية", ""),
                    data.get("الجماعة", ""),
                    data.get("المؤسسة", ""),
                    data.get("السنة_الدراسية", ""),
                    data.get("البلدة", ""),
                    data.get("المدير", "من مدير"),
                    data.get("الحارس_العام", "من حارس عام"),
                    data.get("السلك", "التعليم الابتدائي"),
                    data.get("رقم_الحراسة", "حراسة رقم 1"),
                    data.get("رقم_التسجيل", ""),
                    data.get("الأسدس", "الأول"),
                    current_time
                ))
            
            conn.commit()
            conn.close()
            
            # إرسال إشارة النجاح إلى JavaScript
            self.dataUpdated.emit("success")
            
        except Exception as e:
            # إرسال إشارة الخطأ إلى JavaScript
            self.dataUpdated.emit(f"error:{str(e)}")

    @pyqtSlot()
    def uploadLogo(self):
        """رفع شعار المؤسسة (تخزين المسار فقط، مع معالجة جميع الحالات)"""
        try:
            file_dialog = QFileDialog()
            file_path, _ = file_dialog.getOpenFileName(
                None,
                "اختيار شعار المؤسسة",
                "",
                "Image Files (*.png *.jpg *.jpeg *.bmp *.gif)"
            )
            if not file_path:
                self.emit_log("❌ لم يتم اختيار أي صورة", "error")
                self.logoUpdated.emit("")
                return
            # تحقق من وجود الملف فعليًا
            if not os.path.exists(file_path):
                self.emit_log("❌ ملف الشعار غير موجود على القرص", "error")
                self.logoUpdated.emit("")
                return
            # حفظ المسار في قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
            count = cursor.fetchone()[0]
            if count > 0:
                cursor.execute("UPDATE بيانات_المؤسسة SET الشعار = ? WHERE id = 1", (file_path,))
            else:
                cursor.execute('''
                    INSERT INTO بيانات_المؤسسة (
                        الأكاديمية, المديرية, الجماعة, المؤسسة,
                        السنة_الدراسية, البلدة, المدير, الحارس_العام,
                        السلك, رقم_الحراسة, رقم_التسجيل, الأسدس,
                        الشعار, تاريخ_التحديث
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',                    ("", "", "", "", "", "", "من مدير", "من حارس عام", "التعليم الابتدائي", "حراسة رقم 1", "", "الأول", file_path, datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
                )
            conn.commit()
            conn.close()
            
            # إرسال الشعار للواجهة (base64)
            try:
                with open(file_path, 'rb') as image_file:
                    image_data = image_file.read()
                    base64_string = base64.b64encode(image_data).decode('utf-8')
                    self.logoUpdated.emit(base64_string)
                    self.emit_log("✅ تم رفع شعار المؤسسة بنجاح", "success")
            except Exception as e:
                self.logoUpdated.emit("")
                self.emit_log(f"❌ خطأ في قراءة ملف الشعار: {str(e)}", "error")
        except Exception as e:
            self.logoUpdated.emit("")
            self.emit_log(f"❌ خطأ في رفع الشعار: {str(e)}", "error")

    @pyqtSlot(result=str)
    def getLogo(self):
        """الحصول على شعار المؤسسة (قراءة المسار وتحويله إلى base64)"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT الشعار FROM بيانات_المؤسسة WHERE الشعار IS NOT NULL AND الشعار != '' LIMIT 1")
            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                logo_path = result[0]
                self.emit_log(f"🔍 تم العثور على مسار الشعار: {logo_path}", "info")

                if os.path.exists(logo_path):
                    try:
                        with open(logo_path, 'rb') as image_file:
                            image_data = image_file.read()
                            base64_logo = base64.b64encode(image_data).decode('utf-8')
                            self.emit_log("✅ تم تحميل الشعار بنجاح", "success")
                            return base64_logo
                    except Exception as e:
                        self.emit_log(f"❌ خطأ في قراءة ملف الشعار: {str(e)}", "error")
                        return ""
                else:
                    self.emit_log(f"❌ ملف الشعار غير موجود في المسار: {logo_path}", "error")
                    return ""
            else:
                self.emit_log("⚠️ لا يوجد شعار محفوظ في قاعدة البيانات", "info")
                return ""
        except Exception as e:
            self.emit_log(f"❌ خطأ في جلب الشعار من قاعدة البيانات: {str(e)}", "error")
            return ""

    @pyqtSlot()
    def showInstitutionInfo(self):
        """عرض معلومات المؤسسة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM بيانات_المؤسسة LIMIT 1")
            row = cursor.fetchone()

            if row:
                # التحقق من حالة الشعار
                logo_status = "❌ غير موجود"
                if row[13]:  # عمود الشعار
                    if os.path.exists(row[13]):
                        logo_status = f"✅ موجود ({row[13]})"
                    else:
                        logo_status = f"❌ مسار غير صحيح ({row[13]})"

                info_text = f"""
🏢 معلومات المؤسسة التعليمية

📍 الأكاديمية: {row[1] or 'غير محدد'}
🏛️ المديرية: {row[2] or 'غير محدد'}
🏘️ الجماعة: {row[3] or 'غير محدد'}
🏫 المؤسسة: {row[4] or 'غير محدد'}
📅 السنة الدراسية: {row[5] or 'غير محدد'}
🌆 البلدة: {row[6] or 'غير محدد'}
👨‍💼 المدير: {row[7] or 'غير محدد'}
🛡️ الحارس العام: {row[8] or 'غير محدد'}
🎓 السلك: {row[9] or 'غير محدد'}
🔢 رقم الحراسة: {row[10] or 'غير محدد'}
📋 رقم التسجيل: {row[11] or 'غير محدد'}
📚 الأسدس: {row[12] or 'غير محدد'}
🖼️ الشعار: {logo_status}

🕒 آخر تحديث: {row[14] if len(row) > 14 and row[14] else 'غير محدد'}
                """
            else:
                info_text = "⚠️ لا توجد بيانات محفوظة للمؤسسة"

            conn.close()
            self.emit_log(info_text, "info")

        except Exception as e:
            self.emit_log(f"❌ خطأ في عرض معلومات المؤسسة: {str(e)}", "error")

    @pyqtSlot()
    def checkSystemStatus(self):
        """فحص حالة النظام والشعار"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # فحص قاعدة البيانات
            cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
            count = cursor.fetchone()[0]

            status_text = f"""
🔍 فحص حالة النظام

📊 قاعدة البيانات: {'✅ متصلة' if count >= 0 else '❌ غير متصلة'}
📝 عدد السجلات: {count}
            """

            if count > 0:
                cursor.execute("SELECT الشعار FROM بيانات_المؤسسة LIMIT 1")
                logo_result = cursor.fetchone()

                if logo_result and logo_result[0]:
                    logo_path = logo_result[0]
                    if os.path.exists(logo_path):
                        status_text += f"🖼️ الشعار: ✅ موجود في {logo_path}\n"
                        # قراءة حجم الملف
                        file_size = os.path.getsize(logo_path)
                        status_text += f"📏 حجم الملف: {file_size} بايت\n"
                    else:
                        status_text += f"🖼️ الشعار: ❌ مسار غير صحيح ({logo_path})\n"
                else:
                    status_text += "🖼️ الشعار: ⚠️ غير محفوظ في قاعدة البيانات\n"

            conn.close()
            self.emit_log(status_text, "info")

        except Exception as e:
            self.emit_log(f"❌ خطأ في فحص حالة النظام: {str(e)}", "error")


class InstitutionWindow(QMainWindow):
    """نافذة إدارة بيانات المؤسسة"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🏢 إدارة بيانات المؤسسة")
        self.setGeometry(100, 100, 1000, 650)

        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء محرك المؤسسة
        self.institution_engine = InstitutionEngine()

        # إعداد الواجهة
        self.setup_ui()
        self.setup_web_channel()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء عرض الويب
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)        # تحميل واجهة HTML
        html_content = self.get_complete_html()
        self.web_view.setHtml(html_content)

    def setup_web_channel(self):
        """إعداد قناة التواصل"""
        self.channel = QWebChannel()
        
        # تسجيل الكائن مباشرة في القناة
        self.channel.registerObject("institutionEngine", self.institution_engine)
        
        # ربط القناة بعد تحميل الصفحة
        self.web_view.loadFinished.connect(self.on_page_loaded)

    def on_page_loaded(self):
        """استدعاء عند انتهاء تحميل الصفحة"""
        # تعيين القناة بعد تحميل الصفحة
        self.web_view.page().setWebChannel(self.channel)

    def get_complete_html(self):
        """HTML كامل مع CSS و JavaScript"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <title>إدارة بيانات المؤسسة</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }        body {
            font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
        }.container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 15px;
        }        .logo-header {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }        .logo-header .logo-display {
            width: 250px;
            height: 120px;
            margin: 0;
            border: 2px dashed #ddd;
            border-radius: 8px;
            background: #f9f9f9;
            cursor: pointer;
        }

        .logo-buttons {
            display: flex;
            align-items: center;
        }        .main-content {
            display: block;
            margin-bottom: 20px;
        }

        .form-section {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }.form-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px 20px;
            margin-bottom: 20px;
        }.form-group {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 10px;
        }        .form-group label {
            font-family: 'Calibri', sans-serif;
            font-size: 16px;
            font-weight: bold;
            color: #1e3a8a;
            min-width: 120px;
            text-align: right;
            margin: 0;
        }        .form-group input,
        .form-group select {
            font-family: 'Calibri', sans-serif;
            font-size: 15px;
            font-weight: bold;
            color: #1a1a1a;
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            transition: border-color 0.3s ease;
            flex: 1;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
        }        .logo-display {
            width: 100%;
            height: 150px;
            border: 3px dashed #ddd;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            background: #f9f9f9;
            overflow: hidden;
        }

        .logo-display img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }        .logo-placeholder {
            font-family: 'Calibri', sans-serif;
            font-size: 15px;
            font-weight: bold;
            color: #1a1a1a;
            text-align: center;
        }.button-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }        .btn {
            font-family: 'Calibri', sans-serif;
            font-size: 15px;
            font-weight: bold;
            color: white;
            padding: 8px 18px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(86, 171, 47, 0.4);
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
            color: white;
        }

        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #f8c471 100%);
            color: white;
        }        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
        }@media (max-width: 1024px) {
            .form-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }        @media (max-width: 768px) {
            .logo-header {
                flex-direction: column;
                gap: 15px;
            }
              .logo-header .logo-display {
                width: 220px;
                height: 100px;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .form-group {
                flex-direction: column;
                align-items: stretch;
            }
            
            .form-group label {
                min-width: auto;
                text-align: right;
                margin-bottom: 5px;
            }
            
            .button-group {
                grid-template-columns: 1fr;
            }
        }.section-title {
            font-family: 'Calibri', sans-serif;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #1e3a8a;
            border-bottom: 2px solid #667eea;
            padding-bottom: 8px;
        }        .loading {
            font-family: 'Calibri', sans-serif;
            font-size: 15px;
            font-weight: bold;
            display: none;
            text-align: center;
            color: #667eea;
        }        .loading.show {
            display: block;
        }

        .message-box {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            font-family: 'Calibri', sans-serif;
            font-size: 15px;
            font-weight: bold;
            color: white;
            display: none;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            animation: slideIn 0.3s ease-out;
        }

        .message-box.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }        .message-box.error {
            background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
        }

        .message-box.info {
            background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
        }

        .message-box.show {
            display: block;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <!-- قسم الشعار في الأعلى -->
            <div class="logo-header">
                <div class="logo-display" id="logoDisplay">
                    <div class="logo-placeholder">
                        🏢<br>
                        انقر لرفع الشعار
                    </div>
                </div>
                <div class="logo-buttons">
                    <button type="button" class="btn btn-primary" onclick="uploadLogo()">
                        📤 رفع الشعار
                    </button>
                    <button type="button" class="btn btn-info" onclick="reloadLogo()">
                        🔄 إعادة تحميل
                    </button>
                </div>
            </div>

            <!-- نموذج البيانات -->
            <div class="form-section">
                <h2 class="section-title">📋 بيانات المؤسسة</h2>
                
                <div class="loading" id="loadingIndicator">
                    🔄 جاري تحميل البيانات...
                </div>

                <form id="institutionForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="academy">الأكاديمية:</label>
                            <input type="text" id="academy" name="الأكاديمية" placeholder="أدخل اسم الأكاديمية">
                        </div>

                        <div class="form-group">
                            <label for="directorate">المديرية:</label>
                            <input type="text" id="directorate" name="المديرية" placeholder="أدخل اسم المديرية">
                        </div>

                        <div class="form-group">
                            <label for="community">الجماعة:</label>
                            <input type="text" id="community" name="الجماعة" placeholder="أدخل اسم الجماعة">
                        </div>

                        <div class="form-group">
                            <label for="institution">المؤسسة:</label>
                            <input type="text" id="institution" name="المؤسسة" placeholder="أدخل اسم المؤسسة">
                        </div>

                        <div class="form-group">
                            <label for="academicYear">السنة الدراسية:</label>
                            <select id="academicYear" name="السنة_الدراسية">
                                <option value="">اختر السنة الدراسية</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="city">البلدة:</label>
                            <input type="text" id="city" name="البلدة" placeholder="أدخل اسم البلدة">
                        </div>

                        <div class="form-group">
                            <label for="director">المدير:</label>
                            <select id="director" name="المدير">
                                <option value="من مدير">من مدير</option>
                                <option value="من مديرة">من مديرة</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="guard">الحراسة العامة:</label>
                            <select id="guard" name="الحارس_العام">
                                <option value="من حارس عام">من حارس عام</option>
                                <option value="من حارسة عامة">من حارسة عامة</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="level">السلك:</label>
                            <select id="level" name="السلك">
                                <option value="التعليم الابتدائي">التعليم الابتدائي</option>
                                <option value="الثانوي الإعدادي">الثانوي الإعدادي</option>
                                <option value="الثانوي التأهيلي">الثانوي التأهيلي</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="guardNumber">رقم الحراسة:</label>
                            <select id="guardNumber" name="رقم_الحراسة">
                                <option value="حراسة رقم 1">حراسة رقم 1</option>
                                <option value="حراسة رقم 2">حراسة رقم 2</option>
                                <option value="حراسة رقم 3">حراسة رقم 3</option>
                                <option value="حراسة رقم 4">حراسة رقم 4</option>
                                <option value="حراسة رقم 5">حراسة رقم 5</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="registrationNumber">رقم التسجيل:</label>
                            <input type="text" id="registrationNumber" name="رقم_التسجيل" placeholder="أدخل رقم التسجيل">
                        </div>

                        <div class="form-group">
                            <label for="semester">الأسدس:</label>
                            <select id="semester" name="الأسدس">
                                <option value="الأول">الأول</option>
                                <option value="الثاني">الثاني</option>
                            </select>
                        </div>
                    </div>

                    <div class="button-group">
                        <button type="button" class="btn btn-success" onclick="saveInstitutionData()">
                            💾 حفظ البيانات
                        </button>
                        <button type="button" class="btn btn-info" onclick="loadInstitutionData()">
                            🔄 تحديث البيانات
                        </button>
                        <button type="button" class="btn btn-warning" onclick="showInstitutionInfo()">
                            ℹ️ عرض المعلومات
                        </button>
                        <button type="button" class="btn btn-primary" onclick="checkSystemStatus()">
                            🔍 فحص النظام
                        </button>
                    </div>                </form>
            </div>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <div class="message-box" id="messageBox"></div>
    </div>

    <script>
        let institutionEngine = null;
        let isChannelReady = false;

        // إعداد قناة التواصل مع Python
        function initializeChannel() {
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    institutionEngine = channel.objects.institutionEngine;
                    isChannelReady = true;

                    console.log('🚀 QWebChannel initialized successfully');                    // ربط الإشارات
                    if (institutionEngine) {
                        institutionEngine.logoUpdated.connect(updateLogoDisplay);
                        institutionEngine.dataUpdated.connect(handleDataUpdate);

                        // تحميل البيانات الأولية
                        loadInstitutionData();
                        loadAcademicYears();

                        // تأخير تحميل الشعار قليلاً للتأكد من جاهزية النظام
                        setTimeout(() => {
                            loadLogo();
                        }, 500);

                        console.log('✅ تم تهيئة نظام إدارة المؤسسة بنجاح');
                    }
                });
            } else {
                console.log('⚠️ QWebChannel not available, retrying...');
                setTimeout(initializeChannel, 100);
            }
        }

        // تحميل بيانات المؤسسة
        function loadInstitutionData() {
            if (institutionEngine) {
                showLoading(true);
                institutionEngine.getInstitutionData(function(result) {
                    try {
                        let data;
                        if (typeof result === 'string') {
                            data = JSON.parse(result);
                        } else {
                            data = result;
                        }
                        
                        if (data.error) {
                            console.error('خطأ في تحميل البيانات:', data.error);
                            return;
                        }

                        fillInstitutionForm(data);
                        showLoading(false);
                    } catch (error) {
                        console.error('خطأ في تحليل بيانات المؤسسة:', error);
                        showLoading(false);
                    }
                });
            }
        }

        // تحميل السنوات الدراسية
        function loadAcademicYears() {
            if (institutionEngine) {
                institutionEngine.getAcademicYears(function(result) {
                    try {
                        let years;
                        if (typeof result === 'string') {
                            years = JSON.parse(result);
                        } else {
                            years = result;
                        }
                        
                        const select = document.getElementById('academicYear');
                        select.innerHTML = '<option value="">اختر السنة الدراسية</option>';
                        
                        years.forEach(year => {
                            const option = document.createElement('option');
                            option.value = year;
                            option.textContent = year;
                            select.appendChild(option);
                        });
                    } catch (error) {
                        console.error('خطأ في تحميل السنوات الدراسية:', error);
                    }
                });
            }
        }        // تحميل الشعار
        function loadLogo() {
            if (institutionEngine) {
                console.log("🔄 جاري تحميل الشعار...");
                institutionEngine.getLogo(function(logoData) {
                    console.log("📥 تم استلام الشعار:", logoData ? `بيانات موجودة (${logoData.length} حرف)` : "لا توجد بيانات");
                    if (logoData && logoData.trim() !== '') {
                        console.log("✅ عرض الشعار في الواجهة");
                        updateLogoDisplay(logoData);
                        showMessage("✅ تم تحميل الشعار بنجاح", "success");
                    } else {
                        console.log("⚠️ لا يوجد شعار للعرض");
                        updateLogoDisplay("");
                        showMessage("⚠️ لا يوجد شعار محفوظ", "info");
                    }
                });
            } else {
                console.log("❌ محرك المؤسسة غير متاح");
                showMessage("❌ النظام غير جاهز بعد", "error");
            }
        }

        // ملء نموذج البيانات
        function fillInstitutionForm(data) {
            document.getElementById('academy').value = data.الأكاديمية || '';
            document.getElementById('directorate').value = data.المديرية || '';
            document.getElementById('community').value = data.الجماعة || '';
            document.getElementById('institution').value = data.المؤسسة || '';
            document.getElementById('academicYear').value = data.السنة_الدراسية || '';
            document.getElementById('city').value = data.البلدة || '';
            document.getElementById('director').value = data.المدير || 'من مدير';
            document.getElementById('guard').value = data.الحارس_العام || 'من حارس عام';
            document.getElementById('level').value = data.السلك || 'التعليم الابتدائي';
            document.getElementById('guardNumber').value = data.رقم_الحراسة || 'حراسة رقم 1';
            document.getElementById('registrationNumber').value = data.رقم_التسجيل || '';
            document.getElementById('semester').value = data.الأسدس || 'الأول';
        }        // حفظ بيانات المؤسسة
        function saveInstitutionData() {
            if (institutionEngine) {
                showMessage("🔄 جاري حفظ البيانات...", "info");
                
                const formData = {
                    الأكاديمية: document.getElementById('academy').value,
                    المديرية: document.getElementById('directorate').value,
                    الجماعة: document.getElementById('community').value,
                    المؤسسة: document.getElementById('institution').value,
                    السنة_الدراسية: document.getElementById('academicYear').value,
                    البلدة: document.getElementById('city').value,
                    المدير: document.getElementById('director').value,
                    الحارس_العام: document.getElementById('guard').value,
                    السلك: document.getElementById('level').value,
                    رقم_الحراسة: document.getElementById('guardNumber').value,
                    رقم_التسجيل: document.getElementById('registrationNumber').value,
                    الأسدس: document.getElementById('semester').value
                };

                institutionEngine.saveInstitutionData(JSON.stringify(formData));
            } else {
                showMessage('❌ النظام غير جاهز بعد', 'error');
            }
        }

        // رفع الشعار
        function uploadLogo() {
            if (institutionEngine) {
                showMessage("🔄 جاري رفع الشعار...", "info");
                institutionEngine.uploadLogo();
            } else {
                showMessage('❌ النظام غير جاهز بعد', 'error');
            }
        }

        // عرض معلومات المؤسسة
        function showInstitutionInfo() {
            if (institutionEngine) {
                institutionEngine.showInstitutionInfo();
            } else {
                alert('❌ النظام غير جاهز بعد');
            }
        }

        // فحص حالة النظام
        function checkSystemStatus() {
            if (institutionEngine) {
                showMessage("🔍 جاري فحص حالة النظام...", "info");
                institutionEngine.checkSystemStatus();
            } else {
                showMessage('❌ النظام غير جاهز بعد', 'error');
            }
        }

        // إعادة تحميل الشعار
        function reloadLogo() {
            if (institutionEngine) {
                showMessage("🔄 جاري إعادة تحميل الشعار...", "info");
                loadLogo();
            } else {
                showMessage('❌ النظام غير جاهز بعد', 'error');
            }
        }        // تحديث عرض الشعار
        function updateLogoDisplay(logoData) {
            const logoDisplay = document.getElementById('logoDisplay');

            if (!logoDisplay) {
                console.error("❌ عنصر عرض الشعار غير موجود في الصفحة");
                return;
            }

            if (logoData && logoData.trim() !== '') {
                console.log("🖼️ عرض الشعار في الواجهة");

                // تحديد نوع الصورة بناءً على البيانات
                let imageType = 'jpeg';
                if (logoData.startsWith('/9j/')) imageType = 'jpeg';
                else if (logoData.startsWith('iVBORw0KGgo')) imageType = 'png';
                else if (logoData.startsWith('R0lGOD')) imageType = 'gif';

                logoDisplay.innerHTML = `<img src="data:image/${imageType};base64,${logoData}" alt="شعار المؤسسة" style="max-width: 100%; max-height: 100%; object-fit: contain;">`;
                console.log(`✅ تم تحديث الشعار بنجاح (نوع: ${imageType})`);
            } else {
                console.log("📝 عرض النص البديل للشعار");
                logoDisplay.innerHTML = `
                    <div class="logo-placeholder">
                        🏢<br>
                        انقر لرفع الشعار
                    </div>
                `;
                console.log("⚠️ تم إخفاء الشعار - لا توجد بيانات");
            }
        }

        // معالجة تحديث البيانات
        function handleDataUpdate(result) {
            if (result === "success") {
                showMessage("✅ تم حفظ البيانات بنجاح", "success");
            } else if (result.startsWith("error:")) {
                const errorMsg = result.substring(6);
                showMessage("❌ خطأ في حفظ البيانات: " + errorMsg, "error");
            }
        }

        // عرض الرسائل
        function showMessage(message, type) {
            const messageBox = document.getElementById('messageBox');
            messageBox.textContent = message;
            messageBox.className = `message-box ${type} show`;
            
            // إخفاء الرسالة بعد 3 ثوان
            setTimeout(() => {
                messageBox.classList.remove('show');
            }, 3000);
        }

        // إظهار/إخفاء مؤشر التحميل
        function showLoading(show) {
            const loadingIndicator = document.getElementById('loadingIndicator');
            if (show) {
                loadingIndicator.classList.add('show');
            } else {
                loadingIndicator.classList.remove('show');
            }
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeChannel();
        });

        // إضافة حدث النقر على عرض الشعار لرفع شعار جديد
        document.getElementById('logoDisplay').addEventListener('click', function() {
            uploadLogo();
        });
    </script>
</body>
</html>"""


def main():
    """تشغيل نافذة إدارة بيانات المؤسسة"""
    app = QApplication(sys.argv)

    # إعداد التطبيق
    app.setApplicationName("إدارة بيانات المؤسسة")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Modern Education Systems")

    # إنشاء النافذة
    window = InstitutionWindow()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())


if __name__ == '__main__':
    print("🌐 بدء تشغيل نظام إدارة بيانات المؤسسة...")
    print("=" * 60)
    print("📋 الميزات:")
    print("   🔹 واجهة HTML جميلة ومتجاوبة")
    print("   🔹 إدارة شاملة لبيانات المؤسسة")
    print("   🔹 رفع وإدارة شعار المؤسسة")
    print("   🔹 تكامل كامل مع قاعدة البيانات")
    print("   🔹 تصميم عصري ومرن")
    print("=" * 60)
    print("🚀 جاري تشغيل النظام...")

    try:
        main()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("💡 تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5 PyQtWebEngine")
