#!/usr/bin/env python3
"""
اختبار كائنات الاستيراد الجديدة
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget

# استيراد الكلاس الرئيسي
from main_window_with_import_v2 import ModernEducationalSystemWithTabs

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار كائنات الاستيراد")
        self.setGeometry(100, 100, 1000, 700)
        
        # إنشاء النظام الرئيسي
        self.main_system = ModernEducationalSystemWithTabs()
        
        # إنشاء حاوية للاختبار
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # إنشاء كائنات الاستيراد فقط
        print("🧪 اختبار إنشاء كائنات الاستيراد...")
        import_widget = self.main_system.create_real_import_objects_widget()
        layout.addWidget(import_widget)
        
        print("✅ تم إنشاء كائنات الاستيراد بنجاح!")

if __name__ == "__main__":
    print("🚀 بدء اختبار كائنات الاستيراد...")
    
    app = QApplication(sys.argv)
    
    # إعداد التطبيق
    app.setApplicationName("اختبار كائنات الاستيراد")
    
    # إنشاء النافذة
    window = TestWindow()
    window.show()
    
    print("🎯 النافذة جاهزة - اختبر الأزرار والإحصائيات")
    
    # تشغيل التطبيق
    sys.exit(app.exec_())
