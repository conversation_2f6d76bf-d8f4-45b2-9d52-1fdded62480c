"""
النظام التعليمي الشامل - النافذة الرئيسية مع دمج نافذة الاستيراد
نافذة رئيسية حديثة مع عشرة تبويبات احترافية مع دمج نافذة الاستيراد الفعلية

الميزات:
- عشرة تبويبات احترافية وجميلة
- دمج نافذة الاستيراد الفعلية داخل تبويبة الاستيراد
- تفتح بكامل الشاشة تلقائياً
- تتكيف مع دقة الشاشة
- واجهة HTML + CSS حديثة ومتجاوبة
- ربط Python + HTML باستخدام QWebChannel
- تصميم Material Design احترافي
"""

import sys
import os
import json
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QTabWidget, QHBoxLayout, QLabel, QFrame)
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QIcon, QFont

# استيراد محرك نافذة الاستيراد
from sub1_window_html import DataImportEngine

class MainWindowEngine(QObject):
    """محرك النافذة الرئيسية - مسؤول عن التحكم في التبويبات"""

    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    tabChanged = pyqtSignal(str)  # active tab id

    def __init__(self):
        super().__init__()
        self.current_tab = "dashboard"
        self.db_path = "data.db"

    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.logUpdated.emit(message, status, timestamp)

    @pyqtSlot(str)
    def switchTab(self, tab_id):
        """تبديل التبويبة النشطة"""
        self.current_tab = tab_id
        self.emit_log(f"تم التبديل إلى تبويبة: {tab_id}", "info")
        self.tabChanged.emit(tab_id)

    @pyqtSlot(result=str)
    def getCurrentTab(self):
        """الحصول على التبويبة النشطة حالياً"""
        return self.current_tab

    @pyqtSlot(result=str)
    def getSystemInfo(self):
        """الحصول على معلومات النظام"""
        try:
            system_info = {
                "version": "4.1 - النافذة الرئيسية مع دمج الاستيراد",
                "release_date": "يونيو 2025",
                "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                "database_exists": os.path.exists(self.db_path),
                "tabs_count": 10,
                "current_tab": self.current_tab,
                "import_integrated": True
            }
            return json.dumps(system_info, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"error": str(e)}, ensure_ascii=False)

    @pyqtSlot()
    def showTabInfo(self):
        """عرض معلومات حول التبويبات"""
        info_text = """
🎯 النافذة الرئيسية - عشرة تبويبات احترافية مع دمج الاستيراد

التبويبات المتاحة:
📊 لوحة التحكم - نظرة عامة على النظام
📥 الاستيراد - نافذة الاستيراد الفعلية المدمجة (sub1_window_html.py)
🏢 بيانات المؤسسة - معلومات المؤسسة التعليمية
👥 إدارة الطلاب - قوائم وبيانات الطلاب
👨‍🏫 إدارة المعلمين - بيانات الأساتذة والمعلمين
📚 المناهج والمقررات - إدارة المواد الدراسية
📊 التقارير والإحصائيات - تقارير شاملة ومفصلة
🖨️ الطباعة - إعدادات وقوالب الطباعة
⚙️ الإعدادات - تكوين النظام العام
ℹ️ المساعدة والدعم - أدلة الاستخدام والمساعدة

✨ الجديد: دمج نافذة الاستيراد الفعلية داخل تبويبة الاستيراد
        """
        self.emit_log(info_text, "info")

class MainWindow(QMainWindow):
    """النافذة الرئيسية مع التبويبات والاستيراد المدمج"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎓 النظام التعليمي الشامل - النافذة الرئيسية مع الاستيراد المدمج")
        
        # فتح النافذة في كامل الشاشة
        self.showMaximized()

        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء محرك النافذة
        self.window_engine = MainWindowEngine()
        
        # إنشاء محرك الاستيراد
        self.import_engine = DataImportEngine()

        # إعداد الواجهة
        self.setup_ui()
        self.setup_web_channels()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء مجموعة متراكبة للتبديل بين الواجهات
        self.stacked_widget = QStackedWidget()
        layout.addWidget(self.stacked_widget)

        # إنشاء الواجهة الرئيسية
        self.main_web_view = QWebEngineView()
        self.stacked_widget.addWidget(self.main_web_view)

        # إنشاء واجهة الاستيراد
        self.import_web_view = QWebEngineView()
        self.stacked_widget.addWidget(self.import_web_view)

        # تحميل الواجهات
        self.main_web_view.setHtml(self.get_main_html())
        self.import_web_view.setHtml(self.get_import_html())

        # البدء بالواجهة الرئيسية
        self.stacked_widget.setCurrentWidget(self.main_web_view)

    def setup_web_channels(self):
        """إعداد قنوات التواصل مع JavaScript"""
        
        # قناة الواجهة الرئيسية
        self.main_channel = QWebChannel()
        self.main_channel.registerObject("windowEngine", self.window_engine)
        self.main_web_view.page().setWebChannel(self.main_channel)

        # قناة واجهة الاستيراد
        self.import_channel = QWebChannel()
        self.import_channel.registerObject("importEngine", self.import_engine)
        self.import_web_view.page().setWebChannel(self.import_channel)

        # ربط الإشارات
        self.window_engine.tabChanged.connect(self.on_tab_changed)
        self.main_web_view.loadFinished.connect(self.on_main_page_loaded)
        self.import_web_view.loadFinished.connect(self.on_import_page_loaded)

    def on_main_page_loaded(self):
        """استدعاء عند انتهاء تحميل الصفحة الرئيسية"""
        # تسجيل الكائن مرة واحدة فقط عند تحميل الصفحة
        if not hasattr(self, '_main_channel_registered'):
            self.main_channel.registerObject("windowEngine", self.window_engine)
            self._main_channel_registered = True
            print("✅ تم تسجيل محرك النافذة الرئيسية بنجاح")

    def on_import_page_loaded(self):
        """استدعاء عند انتهاء تحميل صفحة الاستيراد"""
        # تسجيل الكائن مرة واحدة فقط عند تحميل الصفحة
        if not hasattr(self, '_import_channel_registered'):
            self.import_channel.registerObject("importEngine", self.import_engine)
            self._import_channel_registered = True
            print("✅ تم تسجيل محرك الاستيراد بنجاح")

    def on_tab_changed(self, tab_id):
        """معالج تغيير التبويبة"""
        if tab_id == "import":
            # التبديل إلى واجهة الاستيراد المدمجة
            self.stacked_widget.setCurrentWidget(self.import_web_view)
            print("🔄 تم التبديل إلى واجهة الاستيراد المدمجة")
        else:
            # التبديل إلى الواجهة الرئيسية
            self.stacked_widget.setCurrentWidget(self.main_web_view)
            print(f"🔄 تم التبديل إلى الواجهة الرئيسية - تبويبة: {tab_id}")

    def get_import_html(self):
        """الحصول على HTML واجهة الاستيراد"""
        # قراءة HTML من sub1_window_html.py
        try:
            # استخدام محرك الاستيراد الموجود للحصول على HTML
            return self.import_engine.get_html_interface()
        except Exception as e:
            print(f"خطأ في تحميل واجهة الاستيراد: {e}")
            return self.get_fallback_import_html()

    def get_fallback_import_html(self):
        """HTML احتياطي لواجهة الاستيراد في حالة فشل التحميل"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <title>نظام الاستيراد</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            max-width: 800px;
            margin: 0 auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2em;
        }
        .error-message {
            background: #ffe6e6;
            color: #d63031;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #d63031;
            text-align: center;
        }
        .action-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📥 نظام الاستيراد</h1>
        <div class="error-message">
            ⚠️ تعذر تحميل واجهة الاستيراد الأصلية<br>
            يرجى التأكد من وجود ملف sub1_window_html.py
        </div>
        <center>
            <button class="action-button" onclick="goBack()">🔙 العودة للقائمة الرئيسية</button>
        </center>
    </div>
    
    <script>
        function goBack() {
            // سيتم تنفيذ هذا عبر إشارة للنافذة الرئيسية
            if (parent && parent.postMessage) {
                parent.postMessage({action: 'switchTab', tab: 'dashboard'}, '*');
            }
        }
    </script>
</body>
</html>"""

    def get_main_html(self):
        """HTML كامل مع CSS و JavaScript للواجهة الرئيسية"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <title>النظام التعليمي الشامل - النافذة الرئيسية</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            overflow-x: hidden;
        }

        .main-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* رأس النافذة */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            border-bottom: 3px solid #667eea;
        }

        .header h1 {
            font-size: 2.2em;
            color: #2c3e50;
            margin-bottom: 5px;
            font-weight: 700;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        /* منطقة التبويبات */
        .tabs-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.1);
            margin: 20px;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
        }

        /* شريط التبويبات */
        .tabs-bar {
            background: rgba(255, 255, 255, 0.95);
            display: flex;
            overflow-x: auto;
            padding: 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .tab-button {
            flex: 1;
            min-width: 140px;
            padding: 15px 10px;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            color: #7f8c8d;
            font-size: 0.9em;
            font-weight: 600;
            text-align: center;
            border-bottom: 3px solid transparent;
        }

        .tab-button:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            transform: translateY(-2px);
        }

        .tab-button.active {
            color: #667eea;
            background: rgba(102, 126, 234, 0.15);
            border-bottom-color: #667eea;
        }

        .tab-icon {
            display: block;
            font-size: 1.8em;
            margin-bottom: 5px;
        }

        .tab-title {
            display: block;
            font-size: 0.85em;
        }

        /* محتوى التبويبات */
        .tab-content {
            flex: 1;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 30px;
            overflow-y: auto;
        }

        .tab-pane {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .tab-pane.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* تصميم المحتوى */
        .content-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #667eea;
        }

        .content-title {
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .content-description {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        /* بطاقات الميزات */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .feature-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(240, 147, 251, 0.4);
        }

        .feature-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
            display: block;
        }

        .feature-title {
            font-size: 1.2em;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .feature-desc {
            font-size: 0.9em;
            opacity: 0.95;
        }

        /* أزرار الإجراءات */
        .action-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 600;
            margin-top: 20px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .action-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        /* بطاقات الإحصائيات */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1em;
            opacity: 0.9;
        }

        /* تأثيرات خاصة */
        .glow-effect {
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.6) !important;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* التخصيص للشاشات الصغيرة */
        @media (max-width: 768px) {
            .tabs-bar {
                flex-wrap: wrap;
            }
            
            .tab-button {
                min-width: 120px;
                padding: 12px 8px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- رأس النافذة -->
        <div class="header">
            <h1>🎓 النظام التعليمي الشامل</h1>
            <p>النافذة الرئيسية مع دمج نافذة الاستيراد - الإصدار 4.1</p>
        </div>

        <!-- منطقة التبويبات -->
        <div class="tabs-container">
            <!-- شريط التبويبات -->
            <div class="tabs-bar">
                <button class="tab-button active" onclick="switchTab('dashboard')">
                    <span class="tab-icon">📊</span>
                    <span class="tab-title">لوحة التحكم</span>
                </button>
                <button class="tab-button" onclick="switchTab('import')">
                    <span class="tab-icon">📥</span>
                    <span class="tab-title">الاستيراد</span>
                </button>
                <button class="tab-button" onclick="switchTab('institution')">
                    <span class="tab-icon">🏢</span>
                    <span class="tab-title">بيانات المؤسسة</span>
                </button>
                <button class="tab-button" onclick="switchTab('students')">
                    <span class="tab-icon">👥</span>
                    <span class="tab-title">الطلاب</span>
                </button>
                <button class="tab-button" onclick="switchTab('teachers')">
                    <span class="tab-icon">👨‍🏫</span>
                    <span class="tab-title">المعلمين</span>
                </button>
                <button class="tab-button" onclick="switchTab('curriculum')">
                    <span class="tab-icon">📚</span>
                    <span class="tab-title">المناهج</span>
                </button>
                <button class="tab-button" onclick="switchTab('reports')">
                    <span class="tab-icon">📈</span>
                    <span class="tab-title">التقارير</span>
                </button>
                <button class="tab-button" onclick="switchTab('printing')">
                    <span class="tab-icon">🖨️</span>
                    <span class="tab-title">الطباعة</span>
                </button>
                <button class="tab-button" onclick="switchTab('settings')">
                    <span class="tab-icon">⚙️</span>
                    <span class="tab-title">الإعدادات</span>
                </button>
                <button class="tab-button" onclick="switchTab('help')">
                    <span class="tab-icon">ℹ️</span>
                    <span class="tab-title">المساعدة</span>
                </button>
            </div>

            <!-- محتوى التبويبات -->
            <div class="tab-content">
                <!-- لوحة التحكم -->
                <div id="dashboard" class="tab-pane active">
                    <div class="content-card">
                        <h2 class="content-title">📊 لوحة التحكم الرئيسية</h2>
                        <p class="content-description">نظرة شاملة على حالة النظام والإحصائيات العامة</p>
                        
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number">1,234</div>
                                <div class="stat-label">إجمالي الطلاب</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">89</div>
                                <div class="stat-label">المعلمين</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">24</div>
                                <div class="stat-label">الفصول الدراسية</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">12</div>
                                <div class="stat-label">المقررات</div>
                            </div>
                        </div>

                        <div class="features-grid">
                            <div class="feature-card">
                                <span class="feature-icon">🎯</span>
                                <div class="feature-title">الوصول السريع</div>
                                <div class="feature-desc">الوصول المباشر لأهم الميزات</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📈</span>
                                <div class="feature-title">التقارير المرئية</div>
                                <div class="feature-desc">رسوم بيانية وإحصائيات تفاعلية</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🔔</span>
                                <div class="feature-title">الإشعارات</div>
                                <div class="feature-desc">تنبيهات فورية للأحداث المهمة</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- رسالة دمج الاستيراد -->
                <div id="import" class="tab-pane">
                    <div class="content-card">
                        <h2 class="content-title">📥 نافذة الاستيراد المدمجة</h2>
                        <p class="content-description">سيتم تحميل نافذة الاستيراد الفعلية هنا...</p>
                        
                        <div class="features-grid">
                            <div class="feature-card">
                                <span class="feature-icon">✨</span>
                                <div class="feature-title">مدمجة بالكامل</div>
                                <div class="feature-desc">نافذة الاستيراد الأصلية من sub1_window_html.py</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🔗</span>
                                <div class="feature-title">ربط متكامل</div>
                                <div class="feature-desc">تعمل بكامل وظائفها الأصلية</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🚀</span>
                                <div class="feature-title">أداء محسن</div>
                                <div class="feature-desc">تحميل سريع وأداء ممتاز</div>
                            </div>
                        </div>
                        
                        <center>
                            <div style="background: #e8f4f8; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #3498db;">
                                <h3 style="color: #2980b9; margin-bottom: 10px;">🎉 تم دمج نافذة الاستيراد بنجاح!</h3>
                                <p style="color: #34495e; line-height: 1.6;">
                                    عند النقر على تبويبة "الاستيراد" سيتم التبديل تلقائياً إلى نافذة الاستيراد الفعلية 
                                    (sub1_window_html.py) مع الاحتفاظ بجميع وظائفها الأصلية.
                                </p>
                            </div>
                        </center>
                    </div>
                </div>

                <!-- باقي التبويبات -->
                <div id="institution" class="tab-pane">
                    <div class="content-card">
                        <h2 class="content-title">🏢 إدارة بيانات المؤسسة</h2>
                        <p class="content-description">تحديث وإدارة معلومات المؤسسة التعليمية</p>
                        
                        <div class="features-grid">
                            <div class="feature-card">
                                <span class="feature-icon">🏫</span>
                                <div class="feature-title">المعلومات الأساسية</div>
                                <div class="feature-desc">اسم المؤسسة والعنوان والاتصال</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🖼️</span>
                                <div class="feature-title">الشعار والهوية</div>
                                <div class="feature-desc">إدارة شعار المؤسسة والهوية البصرية</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📝</span>
                                <div class="feature-title">الوثائق الرسمية</div>
                                <div class="feature-desc">إدارة الوثائق والمراسلات</div>
                            </div>
                        </div>
                        
                        <button class="action-button" onclick="editInstitution()">✏️ تحرير بيانات المؤسسة</button>
                    </div>
                </div>

                <!-- باقي التبويبات تبقى كما هي... -->
                <div id="students" class="tab-pane">
                    <div class="content-card">
                        <h2 class="content-title">👥 إدارة الطلاب الشاملة</h2>
                        <p class="content-description">نظام متكامل لإدارة بيانات الطلاب ومتابعة أدائهم</p>
                        <button class="action-button" onclick="manageStudents()">👥 إدارة الطلاب</button>
                    </div>
                </div>

                <div id="teachers" class="tab-pane">
                    <div class="content-card">
                        <h2 class="content-title">👨‍🏫 إدارة المعلمين والأساتذة</h2>
                        <p class="content-description">نظام شامل لإدارة بيانات المعلمين والجداول الدراسية</p>
                        <button class="action-button" onclick="manageTeachers()">👨‍🏫 إدارة المعلمين</button>
                    </div>
                </div>

                <div id="curriculum" class="tab-pane">
                    <div class="content-card">
                        <h2 class="content-title">📚 إدارة المناهج والمقررات</h2>
                        <p class="content-description">نظام متطور لإدارة المناهج الدراسية والخطط التعليمية</p>
                        <button class="action-button" onclick="manageCurriculum()">📚 إدارة المناهج</button>
                    </div>
                </div>

                <div id="reports" class="tab-pane">
                    <div class="content-card">
                        <h2 class="content-title">📈 التقارير والإحصائيات</h2>
                        <p class="content-description">تقارير شاملة ومفصلة مع رسوم بيانية تفاعلية</p>
                        <button class="action-button" onclick="generateReports()">📊 إنشاء التقارير</button>
                    </div>
                </div>

                <div id="printing" class="tab-pane">
                    <div class="content-card">
                        <h2 class="content-title">🖨️ نظام الطباعة المتقدم</h2>
                        <p class="content-description">إعدادات طباعة احترافية مع قوالب متنوعة</p>
                        <button class="action-button" onclick="configurePrinting()">🖨️ إعداد الطباعة</button>
                    </div>
                </div>

                <div id="settings" class="tab-pane">
                    <div class="content-card">
                        <h2 class="content-title">⚙️ إعدادات النظام</h2>
                        <p class="content-description">تكوين شامل لجميع جوانب النظام</p>
                        <button class="action-button" onclick="openSettings()">⚙️ فتح الإعدادات</button>
                    </div>
                </div>

                <div id="help" class="tab-pane">
                    <div class="content-card">
                        <h2 class="content-title">ℹ️ المساعدة والدعم</h2>
                        <p class="content-description">أدلة شاملة ودعم فني متكامل</p>
                        <button class="action-button" onclick="showHelp()">❓ عرض المساعدة</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let windowEngine = null;
        let isChannelReady = false;

        // تهيئة قناة التواصل مع Python
        function initializeChannel() {
            new QWebChannel(qt.webChannelTransport, function(channel) {
                windowEngine = channel.objects.windowEngine;
                isChannelReady = true;
                console.log('🔗 تم تأسيس الاتصال مع محرك النافذة بنجاح');
                
                // ربط الإشارات
                if (windowEngine) {
                    windowEngine.logUpdated.connect(addLogEntry);
                    windowEngine.tabChanged.connect(onTabChanged);
                }
            });
        }

        // تبديل التبويبات
        function switchTab(tabId) {
            // إزالة التفعيل من كل التبويبات
            const tabs = document.querySelectorAll('.tab-button');
            const panes = document.querySelectorAll('.tab-pane');
            
            tabs.forEach(tab => tab.classList.remove('active'));
            panes.forEach(pane => pane.classList.remove('active'));
            
            // تفعيل التبويبة المطلوبة
            const activeTab = document.querySelector(`[onclick="switchTab('${tabId}')"]`);
            const activePane = document.getElementById(tabId);
            
            if (activeTab) activeTab.classList.add('active');
            if (activePane) activePane.classList.add('active');
            
            // إرسال إشعار إلى Python
            if (windowEngine && isChannelReady) {
                windowEngine.switchTab(tabId);
            }
            
            console.log(`🔄 تم التبديل إلى تبويبة: ${tabId}`);
        }

        // معالج تغيير التبويبة من Python
        function onTabChanged(tabId) {
            console.log(`📋 تم تغيير التبويبة من Python إلى: ${tabId}`);
        }

        // إضافة رسالة للسجل
        function addLogEntry(message, status, timestamp) {
            console.log(`[${timestamp}] ${status}: ${message}`);
        }

        // دوال الإجراءات
        function editInstitution() {
            console.log('✏️ تحرير بيانات المؤسسة');
            if (windowEngine) {
                windowEngine.emit_log('فتح محرر بيانات المؤسسة', 'info');
            }
        }

        function manageStudents() {
            console.log('👥 إدارة الطلاب');
            if (windowEngine) {
                windowEngine.emit_log('فتح نظام إدارة الطلاب', 'info');
            }
        }

        function manageTeachers() {
            console.log('👨‍🏫 إدارة المعلمين');
            if (windowEngine) {
                windowEngine.emit_log('فتح نظام إدارة المعلمين', 'info');
            }
        }

        function manageCurriculum() {
            console.log('📚 إدارة المناهج');
            if (windowEngine) {
                windowEngine.emit_log('فتح نظام إدارة المناهج', 'info');
            }
        }

        function generateReports() {
            console.log('📊 إنشاء التقارير');
            if (windowEngine) {
                windowEngine.emit_log('فتح مولد التقارير', 'info');
            }
        }

        function configurePrinting() {
            console.log('🖨️ إعداد الطباعة');
            if (windowEngine) {
                windowEngine.emit_log('فتح إعدادات الطباعة', 'info');
            }
        }

        function openSettings() {
            console.log('⚙️ فتح الإعدادات');
            if (windowEngine) {
                windowEngine.emit_log('فتح نافذة الإعدادات', 'info');
            }
        }

        function showHelp() {
            console.log('❓ عرض المساعدة');
            if (windowEngine && isChannelReady) {
                windowEngine.showTabInfo();
            }
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeChannel();
            
            // إضافة تأثيرات تفاعلية
            const cards = document.querySelectorAll('.feature-card, .stat-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.classList.add('glow-effect');
                });
                
                card.addEventListener('mouseleave', function() {
                    this.classList.remove('glow-effect');
                });
            });
        });

        // إضافة تأثير النبض للعناصر المهمة
        setInterval(function() {
            const activeTab = document.querySelector('.tab-button.active');
            if (activeTab) {
                activeTab.classList.add('pulse');
                setTimeout(() => activeTab.classList.remove('pulse'), 2000);
            }
        }, 5000);
    </script>
</body>
</html>"""

def main():
    """تشغيل النافذة الرئيسية مع دمج الاستيراد"""
    app = QApplication(sys.argv)

    # إعداد التطبيق
    app.setApplicationName("النظام التعليمي الشامل - النافذة الرئيسية مع الاستيراد المدمج")
    app.setApplicationVersion("4.1")
    app.setOrganizationName("Modern Education Systems")

    # إنشاء النافذة الرئيسية
    window = MainWindow()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == '__main__':
    print("🌟 النظام التعليمي الشامل - النافذة الرئيسية مع الاستيراد المدمج v4.1")
    print("=" * 80)
    print("🎯 الميزات الجديدة:")
    print("   ✨ عشرة تبويبات احترافية وجميلة")
    print("   🔗 دمج نافذة الاستيراد الفعلية داخل تبويبة الاستيراد")
    print("   🖥️ تفتح بكامل الشاشة تلقائياً")
    print("   📱 تتكيف مع دقة الشاشة")
    print("   🎨 تصميم Material Design حديث")
    print("   🔄 تبديل سلس بين الواجهات")
    print("=" * 80)
    print("📋 التبويبات المتاحة:")
    print("   📊 لوحة التحكم - نظرة عامة على النظام")
    print("   📥 الاستيراد - نافذة الاستيراد الفعلية المدمجة") 
    print("   🏢 بيانات المؤسسة - معلومات المؤسسة التعليمية")
    print("   👥 إدارة الطلاب - قوائم وبيانات الطلاب")
    print("   👨‍🏫 إدارة المعلمين - بيانات الأساتذة والمعلمين")
    print("   📚 المناهج والمقررات - إدارة المواد الدراسية")
    print("   📈 التقارير والإحصائيات - تقارير شاملة ومفصلة")
    print("   🖨️ الطباعة - إعدادات وقوالب الطباعة")
    print("   ⚙️ الإعدادات - تكوين النظام العام")
    print("   ℹ️ المساعدة والدعم - أدلة الاستخدام والمساعدة")
    print("=" * 80)
    print("🚀 جاري تشغيل النافذة الرئيسية مع الاستيراد المدمج...")
    print("📥 تبويبة الاستيراد تحتوي على النافذة الفعلية من sub1_window_html.py")

    try:
        main()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النافذة الرئيسية: {e}")
        print("💡 تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5 PyQtWebEngine")
        print("💡 تأكد من وجود ملف sub1_window_html.py في نفس المجلد")
