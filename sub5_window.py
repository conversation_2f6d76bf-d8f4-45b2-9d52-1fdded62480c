import sqlite3
from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout,
                             QLabel, QPushButton, QFrame, QMessageBox, QGraphicsDropShadowEffect, QDialog, QCalendarWidget)
from PyQt5.QtGui import QFont, QColor, QPainter, QPixmap, QPen, QIcon
from PyQt5.QtCore import Qt, QFile, QRectF
from PyQt5.QtPrintSupport import QPrinter, QPrintPreviewDialog
from datetime import datetime
import os

# استيراد فئة تقرير الإحصائيات من الملف الجديد
from print0 import StatisticsReport

# استيراد وحدة رسائل التأكيد المخصصة
try:
    from sub100_window import ConfirmationDialogs
    CUSTOM_DIALOGS_IMPORTED = True
except ImportError:
    CUSTOM_DIALOGS_IMPORTED = False
    print("تعذر استيراد وحدة رسائل التأكيد المخصصة")

class StatisticsWindow(QWidget):
    """نافذة عرض الإحصائيات العامة للمؤسسة"""

    def __init__(self, parent=None, db_path="data.db"):
        super().__init__(parent)
        # تعديل حجم النافذة إلى 700×600
        self.setFixedSize(700, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.db_path = db_path

        # تعيين الخط الافتراضي للنافذة (Calibri 13 أسود غليظ)
        font = QFont("Calibri", 13)
        font.setBold(True)
        self.setFont(font)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)

        # استخراج السنة الدراسية من جدول بيانات_المؤسسة
        academic_year = self.get_academic_year()

        # إضافة عنوان مع السنة الدراسية
        title_text = "إحصائيات عامة حسب السنة الدراسية " + (academic_year if academic_year else "")
        self.stats_title = QLabel(title_text)
        self.stats_title.setFont(QFont("Calibri", 16, QFont.Bold))
        self.stats_title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.stats_title)

        # استخراج البيانات الإحصائية من قاعدة البيانات حسب السنة الدراسية
        stats_data = self.get_statistics(academic_year)

        # إنشاء الصف الأول من الإحصائيات
        self.stats_layout1 = QHBoxLayout()

        # إطار عدد التلاميذ
        self.students_frame = self.create_stats_card("إجمالي التلاميذ", str(stats_data["students"]), "#2196f3")
        self.stats_layout1.addWidget(self.students_frame)

        # إطار عدد الأقسام
        self.classes_frame = self.create_stats_card("عدد الأقسام", str(stats_data["sections"]), "#4caf50")
        self.stats_layout1.addWidget(self.classes_frame)

        # إطار عدد المستويات
        self.levels_frame = self.create_stats_card("عدد المستويات", str(stats_data["levels"]), "#ff9800")
        self.stats_layout1.addWidget(self.levels_frame)

        # إطار معدل التلاميذ لكل قسم
        avg_students_per_section = 0
        if stats_data["sections"] > 0:
            # تقريب المعدل إلى أقرب عدد صحيح بالتقريب للأدنى
            avg_students_per_section = int(stats_data["students"] / stats_data["sections"])

        self.avg_frame = self.create_stats_card("معدل التلاميذ/قسم", str(avg_students_per_section), "#9c27b0")
        self.stats_layout1.addWidget(self.avg_frame)

        main_layout.addLayout(self.stats_layout1)

        # إضافة صف ثاني للإحصاءات الخاصة بالنوع
        stats_title2 = QLabel("إحصاءات حسب النوع")
        stats_title2.setFont(QFont("Calibri", 14, QFont.Bold))
        stats_title2.setAlignment(Qt.AlignCenter)
        stats_title2.setMargin(10)
        main_layout.addWidget(stats_title2)

        self.stats_layout2 = QHBoxLayout()

        # إطار عدد الذكور
        self.males_frame = self.create_stats_card("عدد الذكور", str(stats_data["males"]), "#3F51B5")
        self.stats_layout2.addWidget(self.males_frame)

        # إطار عدد الإناث
        self.females_frame = self.create_stats_card("عدد الإناث", str(stats_data["females"]), "#E91E63")
        self.stats_layout2.addWidget(self.females_frame)

        # إطار الإجمالي
        total_gender = stats_data["males"] + stats_data["females"]
        self.total_gender_frame = self.create_stats_card("الإجمالي", str(total_gender), "#009688")
        self.stats_layout2.addWidget(self.total_gender_frame)

        # إطار نسبة الإناث
        female_percentage = 0
        if total_gender > 0:
            female_percentage = round((stats_data["females"] / total_gender) * 100, 1)

        self.percentage_frame = self.create_stats_card("نسبة الإناث", f"{female_percentage}%", "#FFC107")
        self.stats_layout2.addWidget(self.percentage_frame)

        main_layout.addLayout(self.stats_layout2)

        # إضافة مساحة فارغة
        main_layout.addStretch()

        # إضافة زر طباعة التقرير
        print_btn = QPushButton("طباعة التقرير")
        print_btn.setFont(QFont("Calibri", 13, QFont.Bold))  # تحديث الخط هنا
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #1976d2;
                color: white;
                border-radius: 4px;
                padding: 10px 20px;
                min-width: 200px;
            }
            QPushButton:hover {
                background-color: #1565c0;
            }
        """)
        print_btn.setFixedHeight(40)  # تقليل ارتفاع الزر ليتناسب مع النافذة الأصغر
        print_btn.clicked.connect(self.print_statistics_report)

        # إضافة الزر بتخطيط أفقي للتوسيط
        btn_layout = QHBoxLayout()
        btn_layout.addStretch()
        btn_layout.addWidget(print_btn)
        btn_layout.addStretch()
        main_layout.addLayout(btn_layout)

    def create_stats_card(self, title, value, color):
        """إنشاء إطار للإحصائيات"""
        frame = QFrame()
        frame.setFrameShape(QFrame.StyledPanel)
        frame.setStyleSheet(f"""
            QFrame {{
                border: none;
                border-radius: 10px;
                background-color: white;
                margin: 5px;
            }}
        """)

        # إنشاء تأثير الظل
        self.apply_shadow(frame)

        # إنشاء التخطيط الداخلي
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(5, 5, 5, 5)  # تقليل الهوامش

        # إضافة عنوان البطاقة
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 13, QFont.Bold))  # تحديث الخط
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName(f"{title.replace(' ', '_').replace('/', '_')}_title")
        layout.addWidget(title_label)

        # إضافة القيمة
        value_label = QLabel(value)
        value_label.setFont(QFont("Calibri", 18, QFont.Bold))  # تعديل حجم الخط
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"color: {color};")
        value_label.setObjectName(f"{title.replace(' ', '_').replace('/', '_')}_value")
        layout.addWidget(value_label)

        # إضافة خط ملون
        color_line = QFrame()
        color_line.setFrameShape(QFrame.HLine)
        color_line.setFixedHeight(4)
        color_line.setStyleSheet(f"background-color: {color};")
        layout.addWidget(color_line)

        return frame

    def apply_shadow(self, widget):
        """إضافة تأثير الظل للعنصر"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(5)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 150))
        widget.setGraphicsEffect(shadow)

    def get_academic_year(self):
        """استخراج السنة الدراسية الحالية من قاعدة البيانات"""
        # إنشاء كائن من فئة StatisticsReport للحصول على السنة الدراسية
        report_generator = StatisticsReport(db_path=self.db_path)
        return report_generator.get_academic_year()

    def get_reference_date(self):
        """طلب تاريخ المرجع من المستخدم"""
        dialog = QDialog(self)
        dialog.setWindowTitle("اختر تاريخ المرجع لحساب الأعمار")
        dialog.setFixedSize(400, 300)

        layout = QVBoxLayout(dialog)

        # إضافة تقويم لاختيار التاريخ
        calendar = QCalendarWidget(dialog)
        calendar.setGridVisible(True)
        calendar.setFirstDayOfWeek(Qt.Saturday)  # بداية الأسبوع يوم السبت
        layout.addWidget(calendar)

        # إضافة زر التأكيد
        btn_ok = QPushButton("تأكيد", dialog)
        btn_ok.clicked.connect(dialog.accept)
        layout.addWidget(btn_ok)

        if dialog.exec_() == QDialog.Accepted:
            return calendar.selectedDate().toPyDate()
        return None

    def get_statistics(self, academic_year=""):
        """استخراج البيانات الإحصائية من قاعدة البيانات"""
        # إنشاء كائن من فئة StatisticsReport للحصول على الإحصائيات
        report_generator = StatisticsReport(db_path=self.db_path)
        return report_generator.get_statistics(academic_year)

    def calculate_age_stats(self, academic_year, reference_date):
        """حساب إحصائيات الأعمار بناءً على تاريخ مرجعي محدد"""
        # إنشاء كائن من فئة StatisticsReport لحساب إحصائيات الأعمار
        report_generator = StatisticsReport(db_path=self.db_path)
        return report_generator.calculate_age_stats(academic_year, reference_date)

    def get_school_info(self):
        """استخراج بيانات المؤسسة من قاعدة البيانات"""
        # إنشاء كائن من فئة StatisticsReport للحصول على بيانات المؤسسة
        report_generator = StatisticsReport(db_path=self.db_path)
        return report_generator.get_school_info()

    def update_statistics(self):
        """تحديث بيانات الإحصائيات"""
        # استخراج السنة الدراسية من جدول بيانات_المؤسسة
        academic_year = self.get_academic_year()

        # تحديث العنوان مع السنة الدراسية
        title_text = "إحصائيات عامة حسب السنة الدراسية " + (academic_year if academic_year else "")
        self.stats_title.setText(title_text)

        # استخراج البيانات الإحصائية من قاعدة البيانات
        stats_data = self.get_statistics(academic_year)

        # تحديث البطاقات
        self.update_stats_cards(stats_data)

    def update_stats_cards(self, stats_data):
        """تحديث قيم بطاقات الإحصائيات"""
        # تحديث إجمالي التلاميذ
        students_value_label = self.students_frame.findChild(QLabel, "إجمالي_التلاميذ_value")
        if students_value_label:
            students_value_label.setText(str(stats_data["students"]))

        # تحديث عدد الأقسام
        sections_value_label = self.classes_frame.findChild(QLabel, "عدد_الأقسام_value")
        if sections_value_label:
            sections_value_label.setText(str(stats_data["sections"]))

        # تحديث عدد المستويات
        levels_value_label = self.levels_frame.findChild(QLabel, "عدد_المستويات_value")
        if levels_value_label:
            levels_value_label.setText(str(stats_data["levels"]))

        # تحديث معدل التلاميذ/قسم
        avg_students_per_section = 0
        if stats_data["sections"] > 0:
            avg_students_per_section = int(stats_data["students"] / stats_data["sections"])
        avg_value_label = self.avg_frame.findChild(QLabel, "معدل_التلاميذ_قسم_value")
        if avg_value_label:
            avg_value_label.setText(str(avg_students_per_section))

        # تحديث عدد الذكور
        males_value_label = self.males_frame.findChild(QLabel, "عدد_الذكور_value")
        if males_value_label:
            males_value_label.setText(str(stats_data["males"]))

        # تحديث عدد الإناث
        females_value_label = self.females_frame.findChild(QLabel, "عدد_الإناث_value")
        if females_value_label:
            females_value_label.setText(str(stats_data["females"]))

        # تحديث الإجمالي
        total_gender = stats_data["males"] + stats_data["females"]
        total_gender_value_label = self.total_gender_frame.findChild(QLabel, "الإجمالي_value")
        if total_gender_value_label:
            total_gender_value_label.setText(str(total_gender))

        # تحديث نسبة الإناث
        female_percentage = 0
        if total_gender > 0:
            female_percentage = round((stats_data["females"] / total_gender) * 100, 1)
        percentage_value_label = self.percentage_frame.findChild(QLabel, "نسبة_الإناث_value")
        if percentage_value_label:
            percentage_value_label.setText(f"{female_percentage}%")

    def print_statistics_report(self):
        """إنشاء تقرير PDF للإحصائيات باستخدام فئة StatisticsReport"""
        try:
            # طلب تاريخ المرجع من المستخدم
            reference_date = self.get_reference_date()
            if not reference_date:
                return

            # إنشاء مجلد رئيسي على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            if not os.path.exists(main_reports_dir):
                os.makedirs(main_reports_dir)

            # إنشاء مجلد فرعي للإحصائيات
            reports_dir = os.path.join(main_reports_dir, "تقرير الاحصائيات")
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            # إنشاء مجلد داخل البرنامج للتوافقية
            local_reports_dir = os.path.join(os.path.dirname(__file__), "التقارير")
            if not os.path.exists(local_reports_dir):
                os.makedirs(local_reports_dir)

            local_stats_dir = os.path.join(local_reports_dir, "إحصائيات")
            if not os.path.exists(local_stats_dir):
                os.makedirs(local_stats_dir)

            # إنشاء اسم الملف مع التاريخ والوقت
            current_datetime = datetime.now()
            date_str = current_datetime.strftime('%Y-%m-%d')
            time_str = current_datetime.strftime('%H-%M-%S')
            filename = f"تقرير_إحصائي_{date_str}_{time_str}.pdf"
            filepath = os.path.join(reports_dir, filename)

            # إنشاء كائن من فئة StatisticsReport
            report_generator = StatisticsReport(db_path=self.db_path)

            # استخراج السنة الدراسية والبيانات الإحصائية
            academic_year = report_generator.get_academic_year()
            stats_data = report_generator.get_statistics(academic_year)

            # حساب إحصائيات الأعمار وإضافتها للبيانات
            age_stats = report_generator.calculate_age_stats(academic_year, reference_date)
            stats_data["age_groups"] = age_stats

            # إنشاء التقرير PDF مع تمرير البيانات المحدثة
            if report_generator.create_pdf_report(filepath, stats_data, reference_date):
                # استخدام نموذج الرسائل المميزة من ملف sub100_window.py إذا كان متوفراً
                if CUSTOM_DIALOGS_IMPORTED:
                    # إنشاء نافذة حوار مخصصة باستخدام فئة ConfirmationDialogs
                    dialog = QDialog(self)
                    dialog.setWindowTitle("تم إنشاء التقرير بنجاح")
                    dialog.setFixedSize(500, 400)
                    dialog.setLayoutDirection(Qt.RightToLeft)

                    # إضافة أيقونة البرنامج
                    try:
                        app_icon = QIcon("01.ico")
                        dialog.setWindowIcon(app_icon)
                    except Exception as e:
                        print(f"خطأ في تحميل أيقونة البرنامج: {e}")

                    # تنسيق النافذة
                    dialog.setStyleSheet("""
                        QDialog {
                            background-color: #f0f8ff;
                            border: 2px solid #0066cc;
                            border-radius: 10px;
                        }
                        QLabel {
                            color: #333333;
                            font-weight: bold;
                        }
                        QTextBrowser {
                            border: 1px solid #3498db;
                            border-radius: 5px;
                            padding: 10px;
                            background-color: white;
                        }
                        QPushButton {
                            border-radius: 5px;
                            padding: 8px 15px;
                            font-weight: bold;
                            min-height: 35px;
                        }
                        QPushButton#preview_btn {
                            background-color: #27ae60;
                            color: white;
                        }
                        QPushButton#preview_btn:hover {
                            background-color: #2ecc71;
                            border: 2px solid #27ae60;
                        }
                        QPushButton#folder_btn {
                            background-color: #3498db;
                            color: white;
                        }
                        QPushButton#folder_btn:hover {
                            background-color: #2980b9;
                            border: 2px solid #3498db;
                        }
                        QPushButton#cancel_btn {
                            background-color: #e74c3c;
                            color: white;
                        }
                        QPushButton#cancel_btn:hover {
                            background-color: #c0392b;
                            border: 2px solid #e74c3c;
                        }
                    """)

                    # إنشاء تخطيط النافذة
                    layout = QVBoxLayout(dialog)
                    layout.setContentsMargins(20, 20, 20, 20)
                    layout.setSpacing(15)

                    # إضافة أيقونة وعنوان
                    header_layout = QHBoxLayout()

                    # محاولة إضافة أيقونة البرنامج
                    icon_label = QLabel()
                    icon_label.setAlignment(Qt.AlignCenter)
                    try:
                        program_icon = QPixmap("01.ico")
                        if not program_icon.isNull():
                            program_icon = program_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                            icon_label.setPixmap(program_icon)
                            header_layout.addWidget(icon_label)
                    except Exception as e:
                        print(f"خطأ في تحميل أيقونة البرنامج للعنوان: {e}")

                    # إضافة عنوان النافذة
                    title_label = QLabel("تم إنشاء التقرير بنجاح")
                    title_label.setFont(QFont("Calibri", 16, QFont.Bold))
                    title_label.setStyleSheet("color: #0066cc;")
                    title_label.setAlignment(Qt.AlignCenter)
                    header_layout.addWidget(title_label, 1)

                    layout.addLayout(header_layout)

                    # إضافة رسالة التأكيد
                    message_label = QLabel(f"تم إنشاء التقرير بنجاح وحفظه في المسار التالي:\n{filepath}")
                    message_label.setFont(QFont("Calibri", 13))
                    message_label.setWordWrap(True)
                    message_label.setAlignment(Qt.AlignRight)
                    message_label.setStyleSheet("margin-top: 10px; margin-bottom: 10px;")
                    layout.addWidget(message_label)

                    question_label = QLabel("هل تريد معاينة التقرير وطباعته الآن؟")
                    question_label.setFont(QFont("Calibri", 13, QFont.Bold))
                    question_label.setAlignment(Qt.AlignRight)
                    question_label.setStyleSheet("color: #0066cc; margin-top: 10px;")
                    layout.addWidget(question_label)

                    # إضافة مساحة فارغة
                    layout.addStretch()

                    # إضافة أزرار التأكيد والإلغاء
                    buttons_layout = QHBoxLayout()

                    preview_btn = QPushButton("معاينة وطباعة")
                    preview_btn.setObjectName("preview_btn")
                    preview_btn.setFont(QFont("Calibri", 12, QFont.Bold))
                    preview_btn.setCursor(Qt.PointingHandCursor)
                    preview_btn.setFixedWidth(120)
                    preview_btn.clicked.connect(lambda: self.handle_report_action(dialog, "preview", filepath))

                    folder_btn = QPushButton("فتح مجلد التقارير")
                    folder_btn.setObjectName("folder_btn")
                    folder_btn.setFont(QFont("Calibri", 12, QFont.Bold))
                    folder_btn.setCursor(Qt.PointingHandCursor)
                    folder_btn.setFixedWidth(120)
                    folder_btn.clicked.connect(lambda: self.handle_report_action(dialog, "folder", reports_dir))

                    cancel_btn = QPushButton("إلغاء")
                    cancel_btn.setObjectName("cancel_btn")
                    cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
                    cancel_btn.setCursor(Qt.PointingHandCursor)
                    cancel_btn.setFixedWidth(120)
                    cancel_btn.clicked.connect(dialog.reject)

                    buttons_layout.addWidget(preview_btn)
                    buttons_layout.addWidget(folder_btn)
                    buttons_layout.addWidget(cancel_btn)

                    layout.addLayout(buttons_layout)

                    # عرض النافذة
                    dialog.exec_()
                else:
                    # استخدام النافذة الافتراضية في حالة عدم توفر وحدة sub100_window
                    msg_box = QMessageBox(self)
                    msg_box.setWindowTitle("تم إنشاء التقرير بنجاح")
                    msg_box.setIcon(QMessageBox.Information)
                    msg_box.setText(f"تم إنشاء التقرير بنجاح وحفظه في المسار التالي:\n{filepath}")
                    msg_box.setInformativeText("هل تريد معاينة التقرير وطباعته الآن؟")

                    # إضافة أزرار مخصصة
                    preview_button = msg_box.addButton("معاينة وطباعة", QMessageBox.ActionRole)
                    open_folder_button = msg_box.addButton("فتح مجلد التقارير", QMessageBox.ActionRole)
                    cancel_button = msg_box.addButton("إلغاء", QMessageBox.RejectRole)

                    # عرض الرسالة وانتظار رد المستخدم
                    msg_box.exec_()

                    # معالجة رد المستخدم
                    if msg_box.clickedButton() == preview_button:
                        # فتح التقرير للمعاينة والطباعة
                        os.startfile(filepath)
                    elif msg_box.clickedButton() == open_folder_button:
                        # فتح مجلد التقارير
                        os.startfile(reports_dir)
            else:
                QMessageBox.warning(self, "تنبيه", "حدث خطأ أثناء إنشاء التقرير")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}")

    # تم نقل دالة create_pdf_report إلى ملف print0.py

    def handle_report_action(self, dialog, action_type, path):
        """معالجة إجراءات نافذة التقرير المخصصة"""
        # إغلاق نافذة الحوار
        dialog.accept()

        # تنفيذ الإجراء المطلوب
        try:
            if action_type == "preview":
                # فتح التقرير للمعاينة والطباعة
                os.startfile(path)
            elif action_type == "folder":
                # فتح مجلد التقارير
                os.startfile(path)
        except Exception as e:
            # عرض رسالة خطأ في حالة فشل فتح الملف أو المجلد
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء محاولة فتح {path}:\n{str(e)}"
            )

    def draw_enhanced_stats_table(self, painter, x, y, width, data, title):
        """رسم جدول إحصائيات محسن"""
        # عنوان القسم
        painter.setFont(QFont("Traditional Arabic", 23, QFont.Bold))
        painter.setPen(QColor("#333333"))
        title_rect = QRectF(x, y, width, 40)
        painter.drawText(title_rect, Qt.AlignRight, title)
        y += 50

        # رسم البطاقات
        card_margin = 10
        card_width = (width - (card_margin * (len(data) - 1))) / len(data)
        card_height = 150

        for i, (label, value, color) in enumerate(data):
            card_x = x + (i * (card_width + card_margin))
            card_rect = QRectF(card_x, y, card_width, card_height)

            # رسم خلفية البطاقة مع ظل
            painter.setPen(Qt.NoPen)
            painter.setBrush(QColor("#FFFFFF"))
            painter.drawRoundedRect(card_rect, 10, 10)

            # رسم القيمة
            painter.setFont(QFont("Traditional Arabic", 24, QFont.Bold))
            painter.setPen(QColor(color))
            value_rect = QRectF(card_x, y + 20, card_width, 60)
            painter.drawText(value_rect, Qt.AlignCenter, value)

            # رسم العنوان
            painter.setFont(QFont("Traditional Arabic", 14))
            painter.setPen(QColor("#333333"))
            label_rect = QRectF(card_x, y + 90, card_width, 40)
            painter.drawText(label_rect, Qt.AlignCenter, label)

            # رسم خط ملون أسفل البطاقة
            painter.setPen(Qt.NoPen)
            painter.setBrush(QColor(color))
            painter.drawRect(
                QRectF(card_x, y + card_height - 5, card_width, 5)
            )

    def draw_stats_table(self, painter, x, y, width, data, title):
        """رسم جدول إحصائيات منسق"""
        # عنوان الجدول
        painter.setFont(QFont("Traditional Arabic", 23, QFont.Bold))
        painter.drawText(QRectF(x, y, width, 35), Qt.AlignRight, title)
        y += 40

        # تنسيق الجدول
        cell_width = width / 2
        row_height = 40

        # رسم الخلفية والحدود
        for i, (label, value) in enumerate(data):
            row_y = y + (i * row_height)

            # رسم خلفية الصف
            if i % 2 == 0:
                painter.fillRect(QRectF(x, row_y, width, row_height),
                               QColor("#f5f5f5"))

            # رسم الحدود
            painter.setPen(QPen(QColor("#cccccc")))

            # تحويل الإحداثيات إلى أعداد صحيحة عند رسم الخطوط
            x1 = int(x)
            x2 = int(x + width)
            y1 = int(row_y)
            y2 = int(row_y + row_height)
            xc = int(x + cell_width)  # نقطة منتصف العرض

            # رسم الإطار الخارجي للصف
            painter.drawLine(x1, y1, x2, y1)  # الخط العلوي
            painter.drawLine(x1, y2, x2, y2)  # الخط السفلي
            painter.drawLine(x1, y1, x1, y2)  # الخط الأيمن
            painter.drawLine(x2, y1, x2, y2)  # الخط الأيسر

            # رسم الخط العمودي في المنتصف
            painter.drawLine(xc, y1, xc, y2)

            # كتابة البيانات
            painter.setFont(QFont("Traditional Arabic", 12))
            painter.setPen(QColor("#000000"))
            painter.drawText(QRectF(x, row_y, cell_width, row_height),
                           Qt.AlignCenter, value)
            painter.drawText(QRectF(x + cell_width, row_y, cell_width, row_height),
                           Qt.AlignCenter, label)

if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    window = StatisticsWindow()
    window.show()
    sys.exit(app.exec_())
