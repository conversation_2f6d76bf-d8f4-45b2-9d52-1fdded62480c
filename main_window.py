"""
النظام التعليمي الشامل - النافذة الرئيسية مع التبويبات
نافذة رئيسية حديثة مع عشرة تبويبات احترافية

الميزات:
- عشرة تبويبات احترافية وجميلة
- تفتح بكامل الشاشة تلقائياً
- تتكيف مع دقة الشاشة
- واجهة HTML + CSS حديثة ومتجاوبة
- ربط Python + HTML باستخدام QWebChannel
- تصميم Material Design احترافي
"""

import sys
import os
import json
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, QTimer
from PyQt5.QtGui import QIcon

class MainWindowEngine(QObject):
    """محرك النافذة الرئيسية - مسؤول عن التحكم في التبويبات"""

    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    tabChanged = pyqtSignal(str)  # active tab id

    def __init__(self):
        super().__init__()
        self.current_tab = "dashboard"
        self.db_path = "data.db"

    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.logUpdated.emit(message, status, timestamp)

    @pyqtSlot(str)
    def switchTab(self, tab_id):
        """تبديل التبويبة النشطة"""
        self.current_tab = tab_id
        self.emit_log(f"تم التبديل إلى تبويبة: {tab_id}", "info")
        self.tabChanged.emit(tab_id)

    @pyqtSlot(result=str)
    def getCurrentTab(self):
        """الحصول على التبويبة النشطة حالياً"""
        return self.current_tab

    @pyqtSlot(result=str)
    def getSystemInfo(self):
        """الحصول على معلومات النظام"""
        try:
            system_info = {
                "version": "4.0 - النافذة الرئيسية مع التبويبات",
                "release_date": "يونيو 2025",
                "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                "database_exists": os.path.exists(self.db_path),
                "tabs_count": 10,
                "current_tab": self.current_tab
            }
            return json.dumps(system_info, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"error": str(e)}, ensure_ascii=False)

    @pyqtSlot()
    def showTabInfo(self):
        """عرض معلومات حول التبويبات"""
        info_text = """
🎯 النافذة الرئيسية - عشرة تبويبات احترافية

التبويبات المتاحة:
📊 لوحة التحكم - نظرة عامة على النظام
📥 الاستيراد - استيراد البيانات والملفات
🏢 بيانات المؤسسة - معلومات المؤسسة التعليمية
👥 إدارة الطلاب - قوائم وبيانات الطلاب
👨‍🏫 إدارة المعلمين - بيانات الأساتذة والمعلمين
📚 المناهج والمقررات - إدارة المواد الدراسية
📊 التقارير والإحصائيات - تقارير شاملة ومفصلة
🖨️ الطباعة - إعدادات وقوالب الطباعة
⚙️ الإعدادات - تكوين النظام العام
ℹ️ المساعدة والدعم - أدلة الاستخدام والمساعدة
        """
        self.emit_log(info_text, "info")

class MainWindow(QMainWindow):
    """النافذة الرئيسية مع التبويبات"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎓 النظام التعليمي الشامل - النافذة الرئيسية")
        
        # فتح النافذة في كامل الشاشة
        self.showMaximized()

        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء محرك النافذة
        self.window_engine = MainWindowEngine()

        # إعداد الواجهة
        self.setup_ui()
        self.setup_web_channel()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء عرض الويب
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)

        # تحميل واجهة HTML
        html_content = self.get_complete_html()
        self.web_view.setHtml(html_content)

    def setup_web_channel(self):
        """إعداد قناة التواصل مع JavaScript"""
        self.channel = QWebChannel()
        self.channel.registerObject("windowEngine", self.window_engine)
        self.web_view.page().setWebChannel(self.channel)

        # انتظار تحميل الصفحة قبل إعداد القناة
        self.web_view.loadFinished.connect(self.on_page_loaded)
    
    def on_page_loaded(self):
        """استدعاء عند انتهاء تحميل الصفحة"""
        # إعادة تسجيل الكائن للتأكد من الربط
        self.channel.registerObject("windowEngine", self.window_engine)
        
        # تأخير إضافي لضمان اكتمال التسجيل
        QTimer.singleShot(500, self.ensure_object_registration)

    def ensure_object_registration(self):
        """ضمان تسجيل الكائن بشكل صحيح"""
        try:
            # إلغاء تسجيل الكائن أولاً
            self.channel.deregisterObject(self.window_engine)
        except:
            pass
        
        # إعادة تسجيل الكائن
        self.channel.registerObject("windowEngine", self.window_engine)
        print("🔄 تم إعادة تسجيل windowEngine للتأكد من الربط الصحيح")

    def get_complete_html(self):
        """HTML كامل مع CSS و JavaScript للنافذة الرئيسية"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <title>النظام التعليمي الشامل - النافذة الرئيسية</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            overflow-x: hidden;
        }

        .main-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* رأس النافذة */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            border-bottom: 3px solid #667eea;
        }

        .header h1 {
            font-size: 2.2em;
            color: #2c3e50;
            margin-bottom: 5px;
            font-weight: 700;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        /* منطقة التبويبات */
        .tabs-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.1);
            margin: 20px;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
        }

        /* شريط التبويبات */
        .tabs-bar {
            background: rgba(255, 255, 255, 0.95);
            display: flex;
            overflow-x: auto;
            padding: 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .tab-button {
            flex: 1;
            min-width: 140px;
            padding: 15px 10px;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            color: #7f8c8d;
            font-size: 0.9em;
            font-weight: 600;
            text-align: center;
            border-bottom: 3px solid transparent;
        }

        .tab-button:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            transform: translateY(-2px);
        }

        .tab-button.active {
            color: #667eea;
            background: rgba(102, 126, 234, 0.15);
            border-bottom-color: #667eea;
        }

        .tab-icon {
            display: block;
            font-size: 1.8em;
            margin-bottom: 5px;
        }

        .tab-title {
            display: block;
            font-size: 0.85em;
        }

        /* محتوى التبويبات */
        .tab-content {
            flex: 1;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 30px;
            overflow-y: auto;
        }

        .tab-pane {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .tab-pane.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* تصميم المحتوى */
        .content-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #667eea;
        }

        .content-title {
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .content-description {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        /* بطاقات الميزات */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .feature-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(240, 147, 251, 0.4);
        }

        .feature-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
            display: block;
        }

        .feature-title {
            font-size: 1.2em;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .feature-desc {
            font-size: 0.9em;
            opacity: 0.9;
        }

        /* بطاقات الإحصائيات */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            display: block;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 1em;
            opacity: 0.9;
        }

        /* أزرار العمل */
        .action-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        /* التذييل */
        .footer {
            background: rgba(255, 255, 255, 0.95);
            padding: 15px;
            text-align: center;
            color: #7f8c8d;
            font-size: 0.9em;
        }

        /* تجاوب مع الشاشات الصغيرة */
        @media (max-width: 768px) {
            .tabs-bar {
                flex-wrap: wrap;
            }
            
            .tab-button {
                min-width: 120px;
                padding: 12px 8px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* تأثيرات إضافية */
        .glow-effect {
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- رأس النافذة -->
        <div class="header">
            <h1>🎓 النظام التعليمي الشامل</h1>
            <p>النافذة الرئيسية - النسخة 4.0 المطورة</p>
        </div>

        <!-- منطقة التبويبات -->
        <div class="tabs-container">
            <!-- شريط التبويبات -->
            <div class="tabs-bar">
                <button class="tab-button active" onclick="switchTab('dashboard')">
                    <span class="tab-icon">📊</span>
                    <span class="tab-title">لوحة التحكم</span>
                </button>
                <button class="tab-button" onclick="switchTab('import')">
                    <span class="tab-icon">📥</span>
                    <span class="tab-title">الاستيراد</span>
                </button>
                <button class="tab-button" onclick="switchTab('institution')">
                    <span class="tab-icon">🏢</span>
                    <span class="tab-title">بيانات المؤسسة</span>
                </button>
                <button class="tab-button" onclick="switchTab('students')">
                    <span class="tab-icon">👥</span>
                    <span class="tab-title">إدارة الطلاب</span>
                </button>
                <button class="tab-button" onclick="switchTab('teachers')">
                    <span class="tab-icon">👨‍🏫</span>
                    <span class="tab-title">إدارة المعلمين</span>
                </button>
                <button class="tab-button" onclick="switchTab('curriculum')">
                    <span class="tab-icon">📚</span>
                    <span class="tab-title">المناهج</span>
                </button>
                <button class="tab-button" onclick="switchTab('reports')">
                    <span class="tab-icon">📈</span>
                    <span class="tab-title">التقارير</span>
                </button>
                <button class="tab-button" onclick="switchTab('printing')">
                    <span class="tab-icon">🖨️</span>
                    <span class="tab-title">الطباعة</span>
                </button>
                <button class="tab-button" onclick="switchTab('settings')">
                    <span class="tab-icon">⚙️</span>
                    <span class="tab-title">الإعدادات</span>
                </button>
                <button class="tab-button" onclick="switchTab('help')">
                    <span class="tab-icon">ℹ️</span>
                    <span class="tab-title">المساعدة</span>
                </button>
            </div>

            <!-- محتوى التبويبات -->
            <div class="tab-content">
                <!-- تبويبة لوحة التحكم -->
                <div id="dashboard" class="tab-pane active">
                    <div class="content-card">
                        <h2 class="content-title">🎯 مرحباً بك في النظام التعليمي الشامل</h2>
                        <p class="content-description">نظرة عامة شاملة على حالة النظام والإحصائيات الرئيسية</p>
                        
                        <div class="stats-grid">
                            <div class="stat-card">
                                <span class="stat-number">1,247</span>
                                <span class="stat-label">إجمالي الطلاب</span>
                            </div>
                            <div class="stat-card">
                                <span class="stat-number">89</span>
                                <span class="stat-label">المعلمين</span>
                            </div>
                            <div class="stat-card">
                                <span class="stat-number">15</span>
                                <span class="stat-label">الأقسام</span>
                            </div>
                            <div class="stat-card">
                                <span class="stat-number">6</span>
                                <span class="stat-label">المستويات</span>
                            </div>
                        </div>

                        <div class="features-grid">
                            <div class="feature-card">
                                <span class="feature-icon">🚀</span>
                                <div class="feature-title">أداء متميز</div>
                                <div class="feature-desc">نظام سريع ومتجاوب مع كافة العمليات</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🔐</span>
                                <div class="feature-title">أمان عالي</div>
                                <div class="feature-desc">حماية شاملة لجميع البيانات</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📱</span>
                                <div class="feature-title">واجهة متجاوبة</div>
                                <div class="feature-desc">تصميم حديث يتكيف مع جميع الأجهزة</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويبة الاستيراد -->
                <div id="import" class="tab-pane">
                    <div class="content-card">
                        <h2 class="content-title">📥 نظام الاستيراد المتقدم</h2>
                        <p class="content-description">استيراد البيانات من مصادر متعددة بسهولة وأمان</p>
                        
                        <div class="features-grid">
                            <div class="feature-card">
                                <span class="feature-icon">📊</span>
                                <div class="feature-title">استيراد Excel</div>
                                <div class="feature-desc">دعم كامل لملفات Excel بجميع الصيغ</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🌐</span>
                                <div class="feature-title">منظومة مسار</div>
                                <div class="feature-desc">استيراد مباشر من منظومة مسار</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📋</span>
                                <div class="feature-title">CSV Files</div>
                                <div class="feature-desc">استيراد ملفات CSV والنصوص المفصولة</div>
                            </div>
                        </div>
                        
                        <button class="action-button" onclick="startImport()">🚀 بدء عملية الاستيراد</button>
                    </div>
                </div>

                <!-- تبويبة بيانات المؤسسة -->
                <div id="institution" class="tab-pane">
                    <div class="content-card">
                        <h2 class="content-title">🏢 إدارة بيانات المؤسسة</h2>
                        <p class="content-description">تحديث وإدارة معلومات المؤسسة التعليمية</p>
                        
                        <div class="features-grid">
                            <div class="feature-card">
                                <span class="feature-icon">🏫</span>
                                <div class="feature-title">المعلومات الأساسية</div>
                                <div class="feature-desc">اسم المؤسسة والعنوان والاتصال</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🖼️</span>
                                <div class="feature-title">الشعار والهوية</div>
                                <div class="feature-desc">إدارة شعار المؤسسة والهوية البصرية</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📝</span>
                                <div class="feature-title">الوثائق الرسمية</div>
                                <div class="feature-desc">إدارة الوثائق والمراسلات</div>
                            </div>
                        </div>
                        
                        <button class="action-button" onclick="editInstitution()">✏️ تحرير بيانات المؤسسة</button>
                    </div>
                </div>

                <!-- تبويبة إدارة الطلاب -->
                <div id="students" class="tab-pane">
                    <div class="content-card">
                        <h2 class="content-title">👥 إدارة الطلاب الشاملة</h2>
                        <p class="content-description">نظام متكامل لإدارة بيانات الطلاب ومتابعة أدائهم</p>
                        
                        <div class="features-grid">
                            <div class="feature-card">
                                <span class="feature-icon">📝</span>
                                <div class="feature-title">سجل الطلاب</div>
                                <div class="feature-desc">إدارة كاملة لبيانات الطلاب الشخصية</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📊</span>
                                <div class="feature-title">الدرجات والتقييم</div>
                                <div class="feature-desc">متابعة الدرجات والتقييمات</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📅</span>
                                <div class="feature-title">الحضور والغياب</div>
                                <div class="feature-desc">تسجيل ومتابعة الحضور اليومي</div>
                            </div>
                        </div>
                        
                        <button class="action-button" onclick="manageStudents()">👥 إدارة الطلاب</button>
                    </div>
                </div>

                <!-- تبويبة إدارة المعلمين -->
                <div id="teachers" class="tab-pane">
                    <div class="content-card">
                        <h2 class="content-title">👨‍🏫 إدارة المعلمين والأساتذة</h2>
                        <p class="content-description">نظام شامل لإدارة بيانات المعلمين والجداول الدراسية</p>
                        
                        <div class="features-grid">
                            <div class="feature-card">
                                <span class="feature-icon">👤</span>
                                <div class="feature-title">بيانات المعلمين</div>
                                <div class="feature-desc">إدارة البيانات الشخصية والمهنية</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📚</span>
                                <div class="feature-title">التخصصات</div>
                                <div class="feature-desc">إدارة التخصصات والمواد المدرسة</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">⏰</span>
                                <div class="feature-title">الجداول الدراسية</div>
                                <div class="feature-desc">توزيع الحصص والجداول</div>
                            </div>
                        </div>
                        
                        <button class="action-button" onclick="manageTeachers()">👨‍🏫 إدارة المعلمين</button>
                    </div>
                </div>

                <!-- تبويبة المناهج -->
                <div id="curriculum" class="tab-pane">
                    <div class="content-card">
                        <h2 class="content-title">📚 إدارة المناهج والمقررات</h2>
                        <p class="content-description">نظام متطور لإدارة المناهج الدراسية والخطط التعليمية</p>
                        
                        <div class="features-grid">
                            <div class="feature-card">
                                <span class="feature-icon">📖</span>
                                <div class="feature-title">المقررات الدراسية</div>
                                <div class="feature-desc">إدارة المواد والمقررات لكل مستوى</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📋</span>
                                <div class="feature-title">الخطط التعليمية</div>
                                <div class="feature-desc">وضع وتطوير الخطط الدراسية</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🎯</span>
                                <div class="feature-title">الأهداف التعليمية</div>
                                <div class="feature-desc">تحديد وقياس الأهداف</div>
                            </div>
                        </div>
                        
                        <button class="action-button" onclick="manageCurriculum()">📚 إدارة المناهج</button>
                    </div>
                </div>

                <!-- تبويبة التقارير -->
                <div id="reports" class="tab-pane">
                    <div class="content-card">
                        <h2 class="content-title">📈 التقارير والإحصائيات</h2>
                        <p class="content-description">تقارير شاملة ومفصلة مع رسوم بيانية تفاعلية</p>
                        
                        <div class="features-grid">
                            <div class="feature-card">
                                <span class="feature-icon">📊</span>
                                <div class="feature-title">التقارير الإحصائية</div>
                                <div class="feature-desc">إحصائيات شاملة عن الأداء</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📉</span>
                                <div class="feature-title">تحليل الأداء</div>
                                <div class="feature-desc">تحليل مفصل لأداء الطلاب</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📋</span>
                                <div class="feature-title">التقارير المخصصة</div>
                                <div class="feature-desc">إنشاء تقارير حسب الحاجة</div>
                            </div>
                        </div>
                        
                        <button class="action-button" onclick="generateReports()">📊 إنشاء التقارير</button>
                    </div>
                </div>

                <!-- تبويبة الطباعة -->
                <div id="printing" class="tab-pane">
                    <div class="content-card">
                        <h2 class="content-title">🖨️ نظام الطباعة المتقدم</h2>
                        <p class="content-description">إعدادات طباعة احترافية مع قوالب متنوعة</p>
                        
                        <div class="features-grid">
                            <div class="feature-card">
                                <span class="feature-icon">📄</span>
                                <div class="feature-title">قوالب الطباعة</div>
                                <div class="feature-desc">قوالب جاهزة لجميع التقارير</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">⚙️</span>
                                <div class="feature-title">إعدادات الطابعة</div>
                                <div class="feature-desc">تكوين متقدم للطابعات</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🎨</span>
                                <div class="feature-title">التصميم المخصص</div>
                                <div class="feature-desc">تخصيص شكل المطبوعات</div>
                            </div>
                        </div>
                        
                        <button class="action-button" onclick="configurePrinting()">🖨️ إعداد الطباعة</button>
                    </div>
                </div>

                <!-- تبويبة الإعدادات -->
                <div id="settings" class="tab-pane">
                    <div class="content-card">
                        <h2 class="content-title">⚙️ إعدادات النظام</h2>
                        <p class="content-description">تكوين شامل لجميع جوانب النظام</p>
                        
                        <div class="features-grid">
                            <div class="feature-card">
                                <span class="feature-icon">🔧</span>
                                <div class="feature-title">الإعدادات العامة</div>
                                <div class="feature-desc">تكوين الإعدادات الأساسية</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">💾</span>
                                <div class="feature-title">النسخ الاحتياطي</div>
                                <div class="feature-desc">إدارة النسخ الاحتياطية</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🔐</span>
                                <div class="feature-title">الأمان والخصوصية</div>
                                <div class="feature-desc">إعدادات الحماية والأمان</div>
                            </div>
                        </div>
                        
                        <button class="action-button" onclick="openSettings()">⚙️ فتح الإعدادات</button>
                    </div>
                </div>

                <!-- تبويبة المساعدة -->
                <div id="help" class="tab-pane">
                    <div class="content-card">
                        <h2 class="content-title">ℹ️ المساعدة والدعم</h2>
                        <p class="content-description">أدلة شاملة ودعم فني متكامل</p>
                        
                        <div class="features-grid">
                            <div class="feature-card">
                                <span class="feature-icon">📖</span>
                                <div class="feature-title">دليل المستخدم</div>
                                <div class="feature-desc">شرح مفصل لجميع الميزات</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🎥</span>
                                <div class="feature-title">فيديوهات تعليمية</div>
                                <div class="feature-desc">دروس مرئية لاستخدام النظام</div>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">💬</span>
                                <div class="feature-title">الدعم الفني</div>
                                <div class="feature-desc">تواصل مباشر مع فريق الدعم</div>
                            </div>
                        </div>
                        
                        <button class="action-button" onclick="showHelp()">❓ عرض المساعدة</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- التذييل -->
        <div class="footer">
            <p>النظام التعليمي الشامل - النافذة الرئيسية v4.0 | تطوير: نظم التعليم الحديثة © 2025</p>
        </div>
    </div>

    <script>
        let windowEngine = null;
        let isChannelReady = false;

        // إعداد قناة التواصل مع Python
        function initializeChannel() {
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    windowEngine = channel.objects.windowEngine;
                    isChannelReady = true;

                    console.log('🚀 QWebChannel initialized successfully');

                    // ربط الإشارات
                    if (windowEngine) {
                        windowEngine.logUpdated.connect(addLogEntry);
                        windowEngine.tabChanged.connect(onTabChanged);

                        console.log('✅ تم تهيئة النافذة الرئيسية بنجاح');
                    }
                });
            } else {
                console.log('⚠️ QWebChannel not available, retrying...');
                setTimeout(initializeChannel, 100);
            }
        }

        // تبديل التبويبات
        function switchTab(tabId) {
            // إخفاء جميع التبويبات
            const allTabs = document.querySelectorAll('.tab-pane');
            const allButtons = document.querySelectorAll('.tab-button');
            
            allTabs.forEach(tab => tab.classList.remove('active'));
            allButtons.forEach(btn => btn.classList.remove('active'));
            
            // إظهار التبويبة المحددة
            const targetTab = document.getElementById(tabId);
            const targetButton = document.querySelector(`[onclick="switchTab('${tabId}')"]`);
            
            if (targetTab) targetTab.classList.add('active');
            if (targetButton) targetButton.classList.add('active');
            
            // إرسال إشعار إلى Python
            if (windowEngine && isChannelReady) {
                windowEngine.switchTab(tabId);
            }
            
            console.log(`🔄 تم التبديل إلى تبويبة: ${tabId}`);
        }

        // معالج تغيير التبويبة من Python
        function onTabChanged(tabId) {
            console.log(`📋 تم تغيير التبويبة من Python إلى: ${tabId}`);
        }

        // إضافة رسالة للسجل
        function addLogEntry(message, status, timestamp) {
            console.log(`[${timestamp}] ${status}: ${message}`);
        }

        // دوال الإجراءات
        function startImport() {
            console.log('🚀 بدء عملية الاستيراد');
            if (windowEngine) {
                windowEngine.emit_log('بدء عملية الاستيراد', 'info');
            }
        }

        function editInstitution() {
            console.log('✏️ تحرير بيانات المؤسسة');
            if (windowEngine) {
                windowEngine.emit_log('فتح محرر بيانات المؤسسة', 'info');
            }
        }

        function manageStudents() {
            console.log('👥 إدارة الطلاب');
            if (windowEngine) {
                windowEngine.emit_log('فتح نظام إدارة الطلاب', 'info');
            }
        }

        function manageTeachers() {
            console.log('👨‍🏫 إدارة المعلمين');
            if (windowEngine) {
                windowEngine.emit_log('فتح نظام إدارة المعلمين', 'info');
            }
        }

        function manageCurriculum() {
            console.log('📚 إدارة المناهج');
            if (windowEngine) {
                windowEngine.emit_log('فتح نظام إدارة المناهج', 'info');
            }
        }

        function generateReports() {
            console.log('📊 إنشاء التقارير');
            if (windowEngine) {
                windowEngine.emit_log('فتح مولد التقارير', 'info');
            }
        }

        function configurePrinting() {
            console.log('🖨️ إعداد الطباعة');
            if (windowEngine) {
                windowEngine.emit_log('فتح إعدادات الطباعة', 'info');
            }
        }

        function openSettings() {
            console.log('⚙️ فتح الإعدادات');
            if (windowEngine) {
                windowEngine.emit_log('فتح نافذة الإعدادات', 'info');
            }
        }

        function showHelp() {
            console.log('❓ عرض المساعدة');
            if (windowEngine && isChannelReady) {
                windowEngine.showTabInfo();
            }
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeChannel();
            
            // إضافة تأثيرات تفاعلية
            const cards = document.querySelectorAll('.feature-card, .stat-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.classList.add('glow-effect');
                });
                
                card.addEventListener('mouseleave', function() {
                    this.classList.remove('glow-effect');
                });
            });
        });

        // إضافة تأثير النبض للعناصر المهمة
        setInterval(function() {
            const activeTab = document.querySelector('.tab-button.active');
            if (activeTab) {
                activeTab.classList.add('pulse');
                setTimeout(() => activeTab.classList.remove('pulse'), 2000);
            }
        }, 5000);
    </script>
</body>
</html>"""

def main():
    """تشغيل النافذة الرئيسية"""
    app = QApplication(sys.argv)

    # إعداد التطبيق
    app.setApplicationName("النظام التعليمي الشامل - النافذة الرئيسية")
    app.setApplicationVersion("4.0")
    app.setOrganizationName("Modern Education Systems")

    # إنشاء النافذة الرئيسية
    window = MainWindow()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == '__main__':
    print("🌟 النظام التعليمي الشامل - النافذة الرئيسية v4.0")
    print("=" * 70)
    print("🎯 الميزات الجديدة:")
    print("   ✨ عشرة تبويبات احترافية وجميلة")
    print("   🖥️ تفتح بكامل الشاشة تلقائياً")
    print("   📱 تتكيف مع دقة الشاشة")
    print("   🎨 تصميم Material Design حديث")
    print("   🔗 ربط Python + HTML متطور")
    print("=" * 70)
    print("📋 التبويبات المتاحة:")
    print("   📊 لوحة التحكم - نظرة عامة على النظام")
    print("   📥 الاستيراد - استيراد البيانات والملفات") 
    print("   🏢 بيانات المؤسسة - معلومات المؤسسة التعليمية")
    print("   👥 إدارة الطلاب - قوائم وبيانات الطلاب")
    print("   👨‍🏫 إدارة المعلمين - بيانات الأساتذة والمعلمين")
    print("   📚 المناهج والمقررات - إدارة المواد الدراسية")
    print("   📈 التقارير والإحصائيات - تقارير شاملة ومفصلة")
    print("   🖨️ الطباعة - إعدادات وقوالب الطباعة")
    print("   ⚙️ الإعدادات - تكوين النظام العام")
    print("   ℹ️ المساعدة والدعم - أدلة الاستخدام والمساعدة")
    print("=" * 70)
    print("🚀 جاري تشغيل النافذة الرئيسية...")

    try:
        main()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النافذة الرئيسية: {e}")
        print("💡 تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5 PyQtWebEngine")
