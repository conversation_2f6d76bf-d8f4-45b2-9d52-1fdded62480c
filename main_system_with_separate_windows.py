"""
النظام التعليمي الشامل - النوافذ المنفصلة
نافذة رئيسية بسيطة تفتح النوافذ المنفصلة

الميزات:
- نافذة رئيسية بسيطة وأنيقة
- فتح النوافذ المنفصلة حسب الحاجة
- أداء ممتاز وذاكرة أقل
- سهولة الصيانة والتطوير
- واجهة HTML جميلة ومتجاوبة
"""

import sys
import os
import json
import sqlite3
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, QTimer, QDateTime
from PyQt5.QtGui import QIcon

class MainSystemEngine(QObject):
    """محرك النظام الرئيسي - مسؤول عن فتح النوافذ المنفصلة"""

    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    systemStatusUpdated = pyqtSignal(str)  # system status JSON

    def __init__(self):
        super().__init__()
        self.db_path = "data.db"
        self.main_window = None
          # مراجع للنوافذ المنفصلة
        self.import_window = None
        self.settings_window = None
        self.institution_window = None
        self.statistics_window = None
        self.sub551_window = None  # النافذة التجريبية

    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.logUpdated.emit(message, status, timestamp)

    @pyqtSlot(result=str)
    def getSystemStatus(self):
        """الحصول على حالة النظام"""
        try:
            status = {
                "database": {
                    "exists": os.path.exists(self.db_path),
                    "size_mb": self.get_file_size(self.db_path) if os.path.exists(self.db_path) else 0,
                    "tables_count": self.get_tables_count()
                },
                "windows": {
                    "import_available": True,
                    "settings_available": True,
                    "institution_available": True,
                    "statistics_available": True
                },
                "system_info": {
                    "version": "3.0 - النوافذ المنفصلة",
                    "last_update": "يونيو 2025",
                    "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
                }
            }
            return json.dumps(status, ensure_ascii=False)
        except Exception as e:
            self.emit_log(f"خطأ في جمع حالة النظام: {str(e)}", "error")
            return json.dumps({"error": str(e)}, ensure_ascii=False)

    def get_file_size(self, file_path):
        """الحصول على حجم الملف بالميجابايت"""
        try:
            size_bytes = os.path.getsize(file_path)
            return round(size_bytes / (1024 * 1024), 2)
        except:
            return 0

    def get_tables_count(self):
        """عدد الجداول في قاعدة البيانات"""
        try:
            if not os.path.exists(self.db_path):
                return 0

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except:
            return 0

    @pyqtSlot(result=str)
    def getDatabaseStatistics(self):
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            if not os.path.exists(self.db_path):
                stats = {
                    "students_count": 0,
                    "teachers_count": 0,
                    "sections_count": 0,
                    "levels_count": 0,
                    "secret_codes_count": 0,
                    "current_year": "غير محدد"
                }
            else:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                stats = {}
                
                try:
                    # السنة الدراسية الحالية
                    cursor.execute("SELECT DISTINCT السنة_الدراسية FROM اللوائح ORDER BY السنة_الدراسية DESC LIMIT 1")
                    result = cursor.fetchone()
                    stats['current_year'] = result[0] if result and result[0] else "غير محدد"
                    
                    # إحصائيات الطلاب
                    cursor.execute("SELECT COUNT(*) FROM اللوائح")
                    stats['students_count'] = cursor.fetchone()[0]
                    
                    # إحصائيات الأساتذة
                    cursor.execute("SELECT COUNT(*) FROM الأساتذة")
                    stats['teachers_count'] = cursor.fetchone()[0]
                    
                    # إحصائيات الأقسام
                    cursor.execute("SELECT COUNT(DISTINCT القسم) FROM اللوائح")
                    stats['sections_count'] = cursor.fetchone()[0]
                    
                    # إحصائيات المستويات
                    cursor.execute("SELECT COUNT(DISTINCT المستوى) FROM اللوائح")
                    stats['levels_count'] = cursor.fetchone()[0]
                    
                    # إحصائيات الرموز السرية
                    cursor.execute("SELECT COUNT(*) FROM الرمز_السري")
                    stats['secret_codes_count'] = cursor.fetchone()[0]
                    
                except Exception as e:
                    print(f"خطأ في جمع الإحصائيات: {e}")
                    stats = {
                        "students_count": 0,
                        "teachers_count": 0,
                        "sections_count": 0,
                        "levels_count": 0,
                        "secret_codes_count": 0,
                        "current_year": "غير محدد"
                    }
                finally:
                    conn.close()
            
            return json.dumps(stats, ensure_ascii=False)
        except Exception as e:
            self.emit_log(f"خطأ في جمع إحصائيات قاعدة البيانات: {str(e)}", "error")
            return json.dumps({"error": str(e)}, ensure_ascii=False)

    @pyqtSlot()
    def openImportWindow(self):
        """فتح نافذة الاستيراد المنفصلة - تجربة sub551"""
        self.emit_log("🔄 فتح نافذة الاستيراد التجريبية...", "info")
        # فتح نافذة sub551 بدلاً من نافذة الاستيراد الأصلية
        self.openSub551Window()

    @pyqtSlot()
    def openSettingsWindow(self):
        """فتح نافذة الإعدادات المنفصلة"""
        self.emit_log("🔄 فتح نافذة الإعدادات...", "info")
        
        try:
            from settings_engine import SettingsEngine
            
            # إنشاء نافذة الإعدادات إذا لم تكن موجودة
            if self.settings_window is None:
                self.settings_window = SettingsEngine(parent_window=self.main_window)
            
            # عرض النافذة
            if hasattr(self.settings_window, 'show'):
                self.settings_window.show()
                self.settings_window.activateWindow()
                self.settings_window.raise_()
                self.emit_log("✅ تم فتح نافذة الإعدادات بنجاح", "success")
            else:
                self.emit_log("❌ خطأ: نافذة الإعدادات لا تحتوي على دالة عرض", "error")
                
        except ImportError:
            self.emit_log("❌ خطأ: ملف settings_engine.py غير موجود", "error")
        except Exception as e:
            self.emit_log(f"❌ خطأ في فتح نافذة الإعدادات: {str(e)}", "error")

    @pyqtSlot()
    def openInstitutionWindow(self):
        """فتح نافذة بيانات المؤسسة المنفصلة"""
        self.emit_log("🔄 فتح نافذة بيانات المؤسسة...", "info")
        
        try:
            from sub2_window import SubWindow
            
            # إنشاء نافذة المؤسسة إذا لم تكن موجودة
            if self.institution_window is None:
                self.institution_window = SubWindow(parent=self.main_window)
            
            # عرض النافذة
            if hasattr(self.institution_window, 'show'):
                self.institution_window.show()
                self.institution_window.activateWindow()
                self.institution_window.raise_()
                self.emit_log("✅ تم فتح نافذة بيانات المؤسسة بنجاح", "success")
            else:
                self.emit_log("❌ خطأ: نافذة المؤسسة لا تحتوي على دالة عرض", "error")
                
        except ImportError:
            self.emit_log("❌ خطأ: ملف sub2_window.py غير موجود", "error")
        except Exception as e:
            self.emit_log(f"❌ خطأ في فتح نافذة بيانات المؤسسة: {str(e)}", "error")

    @pyqtSlot()
    def openStatisticsWindow(self):
        """فتح نافذة الإحصائيات المنفصلة"""
        self.emit_log("🔄 فتح نافذة الإحصائيات...", "info")
        
        try:
            # يمكن إضافة نافذة الإحصائيات هنا لاحقاً
            self.emit_log("⚠️ نافذة الإحصائيات قيد التطوير", "warning")
                
        except Exception as e:
            self.emit_log(f"❌ خطأ في فتح نافذة الإحصائيات: {str(e)}", "error")    @pyqtSlot()
    def openSub551Window(self):
        """فتح نافذة sub551 المنفصلة"""
        self.emit_log("🔄 فتح نافذة sub551...", "info")
        
        try:
            from sub551_window import ModernSub1Window
            
            # إنشاء نافذة sub551 إذا لم تكن موجودة
            if not hasattr(self, 'sub551_window') or self.sub551_window is None:
                self.sub551_window = ModernSub1Window()
            
            # عرض النافذة في وسط الشاشة
            if hasattr(self.sub551_window, 'show'):
                self.sub551_window.show()
                self.sub551_window.activateWindow()
                self.sub551_window.raise_()
                
                # تحريك النافذة إلى وسط الشاشة
                if hasattr(self.sub551_window, 'move'):
                    screen = self.main_window.screen()
                    screen_rect = screen.availableGeometry()
                    window_rect = self.sub551_window.geometry()
                    center_x = (screen_rect.width() - window_rect.width()) // 2
                    center_y = (screen_rect.height() - window_rect.height()) // 2
                    self.sub551_window.move(center_x, center_y)
                
                self.emit_log("✅ تم فتح نافذة sub551 بنجاح", "success")
            else:
                self.emit_log("❌ خطأ: نافذة sub551 لا تحتوي على دالة عرض", "error")
                
        except ImportError:
            self.emit_log("❌ خطأ: ملف sub551_window.py غير موجود", "error")
        except Exception as e:
            self.emit_log(f"❌ خطأ في فتح نافذة sub551: {str(e)}", "error")

    @pyqtSlot()
    def showAbout(self):
        """عرض معلومات حول النظام"""
        about_text = """
🎓 النظام التعليمي الشامل - النوافذ المنفصلة

النسخة: 3.0
التطوير: يونيو 2025
المطور: نظم التعليم الحديثة

الميزات:
• نافذة رئيسية بسيطة وأنيقة
• نوافذ منفصلة لكل وحدة
• أداء ممتاز وذاكرة أقل
• سهولة الصيانة والتطوير
• واجهة HTML جميلة ومتجاوبة

الوحدات المتاحة:
📥 نافذة الاستيراد
⚙️ نافذة الإعدادات  
🏢 نافذة بيانات المؤسسة
📊 نافذة الإحصائيات (قيد التطوير)
        """
        self.emit_log(about_text, "info")

class MainSystemWindow(QMainWindow):
    """النافذة الرئيسية للنظام"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎓 النظام التعليمي الشامل - النوافذ المنفصلة")
        
        # فتح النافذة في كامل الشاشة
        self.showMaximized()

        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء محرك النظام
        self.system_engine = MainSystemEngine()
        self.system_engine.main_window = self

        # إعداد الواجهة
        self.setup_ui()
        self.setup_web_channel()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء عرض الويب
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)

        # تحميل واجهة HTML
        html_content = self.get_complete_html()
        self.web_view.setHtml(html_content)

    def setup_web_channel(self):
        """إعداد قناة التواصل مع JavaScript"""
        self.channel = QWebChannel()
        self.channel.registerObject("systemEngine", self.system_engine)
        self.web_view.page().setWebChannel(self.channel)

        # انتظار تحميل الصفحة قبل إعداد القناة
        self.web_view.loadFinished.connect(self.on_page_loaded)

    def on_page_loaded(self):
        """استدعاء عند انتهاء تحميل الصفحة"""
        # إعادة تسجيل الكائن للتأكد من الربط
        self.channel.registerObject("systemEngine", self.system_engine)

    def get_complete_html(self):
        """HTML كامل مع CSS و JavaScript"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <title>النظام التعليمي الشامل - النوافذ المنفصلة</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 50%, #f5f5dc 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }        .header {
            text-align: center;
            margin-bottom: 40px;
            color: #2c3e50;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.8;
            color: #34495e;
        }        .system-info {
            background: rgba(255,255,255,0.7);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
            color: #2c3e50;
            border: 1px solid rgba(52, 73, 94, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }        .stat-card {
            background: linear-gradient(135deg, #87ceeb 0%, #b0e0e6 100%);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            color: #2c3e50;
            transition: transform 0.3s ease;
            border: 1px solid rgba(135, 206, 235, 0.3);
            box-shadow: 0 4px 15px rgba(135, 206, 235, 0.2);
        }        .stat-card:hover {
            transform: translateY(-5px);
            background: linear-gradient(135deg, #87ceeb 0%, #add8e6 100%);
            box-shadow: 0 8px 25px rgba(135, 206, 235, 0.4);
        }

        .year-card {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%) !important;
            border: 1px solid rgba(255, 154, 158, 0.4) !important;
            box-shadow: 0 4px 15px rgba(255, 154, 158, 0.3) !important;
        }

        .year-card:hover {
            background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%) !important;
            box-shadow: 0 8px 25px rgba(255, 154, 158, 0.5) !important;
        }

        .year-card .stat-number {
            color: #c0392b !important;
        }

        .year-card .stat-label {
            color: #2c3e50 !important;
        }.stat-number {
            display: block;
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
            color: #1e5799;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
            color: #2c3e50;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }        .module-card {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: 1px solid rgba(52, 73, 94, 0.1);
        }

        .module-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            background: rgba(255,255,255,0.95);
        }

        .module-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .module-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }        .module-description {
            color: #7f8c8d;
            line-height: 1.5;
        }        .log-container {
            background: rgba(44, 62, 80, 0.9);
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid rgba(52, 73, 94, 0.3);
        }

        .log-entry {
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-bottom: 5px;
        }

        .log-entry.error {
            color: #ff4444;
        }

        .log-entry.warning {
            color: #ffaa00;
        }

        .log-entry.success {
            color: #00ff88;
        }        .footer {
            text-align: center;
            color: rgba(52, 73, 94, 0.8);
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(52, 73, 94, 0.2);
        }

        @media (max-width: 768px) {
            .modules-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- الرأس -->
        <div class="header">
            <h1>🎓 النظام التعليمي الشامل</h1>
            <p>النوافذ المنفصلة - نسخة 3.0</p>
        </div>

        <!-- معلومات النظام -->
        <div class="system-info">
            <h3>💡 الميزات الجديدة</h3>
            <p>نافذة رئيسية بسيطة تفتح النوافذ المنفصلة • أداء ممتاز وذاكرة أقل • سهولة الصيانة والتطوير</p>
        </div>        <!-- الإحصائيات -->
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <span class="stat-number" id="studentsCount">0</span>
                <span class="stat-label">👥 الطلاب</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="teachersCount">0</span>
                <span class="stat-label">👨‍🏫 الأساتذة</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="sectionsCount">0</span>
                <span class="stat-label">🏫 الأقسام</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="levelsCount">0</span>
                <span class="stat-label">📚 المستويات</span>
            </div>            <div class="stat-card year-card">
                <span class="stat-number" id="currentYear">غير محدد</span>
                <span class="stat-label">📅 السنة الدراسية</span>
            </div>
        </div>

        <!-- وحدات النظام -->
        <div class="modules-grid">
            <div class="module-card" onclick="openImportWindow()">
                <div class="module-icon">📥</div>
                <div class="module-title">نظام الاستيراد</div>
                <div class="module-description">استيراد البيانات من ملفات Excel ومنظومة مسار</div>
            </div>

            <div class="module-card" onclick="openSettingsWindow()">
                <div class="module-icon">⚙️</div>
                <div class="module-title">الإعدادات</div>
                <div class="module-description">النسخ الاحتياطي والصيانة وإعدادات النظام</div>
            </div>

            <div class="module-card" onclick="openInstitutionWindow()">
                <div class="module-icon">🏢</div>
                <div class="module-title">بيانات المؤسسة</div>
                <div class="module-description">إدارة معلومات المؤسسة والشعار والبيانات</div>
            </div>

            <div class="module-card" onclick="openStatisticsWindow()">
                <div class="module-icon">📊</div>
                <div class="module-title">الإحصائيات</div>
                <div class="module-description">التقارير والإحصائيات الشاملة (قيد التطوير)</div>
            </div>
        </div>

        <!-- سجل الأنشطة -->
        <div class="log-container" id="logContainer">
            <div class="log-entry">🚀 تم تحميل النظام بنجاح</div>
        </div>

        <!-- التذييل -->
        <div class="footer">
            <p>النظام التعليمي الشامل - النوافذ المنفصلة v3.0 | تطوير: نظم التعليم الحديثة</p>
            <p><a href="#" onclick="showAbout()" style="color: rgba(52, 73, 94, 0.8);">معلومات حول النظام</a></p>
        </div>
    </div>

    <script>
        let systemEngine = null;
        let isChannelReady = false;

        // إعداد قناة التواصل مع Python
        function initializeChannel() {
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    systemEngine = channel.objects.systemEngine;
                    isChannelReady = true;

                    console.log('🚀 QWebChannel initialized successfully');

                    // ربط الإشارات
                    if (systemEngine) {
                        systemEngine.logUpdated.connect(addLogEntry);
                        systemEngine.systemStatusUpdated.connect(updateSystemStatus);

                        // تحميل الإحصائيات
                        loadDatabaseStatistics();

                        console.log('✅ تم تهيئة النظام بنجاح');
                    }
                });
            } else {
                console.log('⚠️ QWebChannel not available, retrying...');
                setTimeout(initializeChannel, 100);
            }
        }

        // تحميل إحصائيات قاعدة البيانات
        function loadDatabaseStatistics() {
            if (systemEngine) {
                systemEngine.getDatabaseStatistics(function(result) {
                    try {
                        let stats;
                        if (typeof result === 'string') {
                            stats = JSON.parse(result);
                        } else {
                            stats = result;
                        }
                        
                        if (stats.error) {
                            console.error('خطأ في تحميل الإحصائيات:', stats.error);
                            return;
                        }

                        updateStatistics(stats);
                    } catch (error) {
                        console.error('خطأ في تحليل بيانات الإحصائيات:', error);
                    }
                });
            }
        }        // تحديث الإحصائيات في الواجهة
        function updateStatistics(stats) {
            document.getElementById('studentsCount').textContent = stats.students_count || 0;
            document.getElementById('teachersCount').textContent = stats.teachers_count || 0;
            document.getElementById('sectionsCount').textContent = stats.sections_count || 0;
            document.getElementById('levelsCount').textContent = stats.levels_count || 0;
            document.getElementById('currentYear').textContent = stats.current_year || 'غير محدد';
        }

        // إضافة رسالة للسجل
        function addLogEntry(message, status, timestamp) {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry ' + status;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;

            // الاحتفاظ بآخر 50 رسالة فقط
            const entries = logContainer.querySelectorAll('.log-entry');
            if (entries.length > 50) {
                logContainer.removeChild(entries[0]);
            }
        }

        // تحديث حالة النظام
        function updateSystemStatus(statusJson) {
            // يمكن إضافة منطق تحديث حالة النظام هنا
        }

        // فتح نافذة الاستيراد
        function openImportWindow() {
            if (systemEngine) {
                systemEngine.openImportWindow();
            } else {
                alert('❌ النظام غير جاهز بعد');
            }
        }

        // فتح نافذة الإعدادات
        function openSettingsWindow() {
            if (systemEngine) {
                systemEngine.openSettingsWindow();
            } else {
                alert('❌ النظام غير جاهز بعد');
            }
        }

        // فتح نافذة بيانات المؤسسة
        function openInstitutionWindow() {
            if (systemEngine) {
                systemEngine.openInstitutionWindow();
            } else {
                alert('❌ النظام غير جاهز بعد');
            }
        }

        // فتح نافذة الإحصائيات
        function openStatisticsWindow() {
            if (systemEngine) {
                systemEngine.openStatisticsWindow();
            } else {
                alert('❌ النظام غير جاهز بعد');
            }
        }

        // عرض معلومات حول النظام
        function showAbout() {
            if (systemEngine) {
                systemEngine.showAbout();
            } else {
                alert('النظام التعليمي الشامل - النوافذ المنفصلة v3.0');
            }
        }

        // تحديث الإحصائيات كل 30 ثانية
        setInterval(function() {
            if (isChannelReady) {
                loadDatabaseStatistics();
            }
        }, 30000);

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeChannel();
        });
    </script>
</body>
</html>"""

def main():
    """تشغيل النظام الرئيسي"""
    app = QApplication(sys.argv)

    # إعداد التطبيق
    app.setApplicationName("النظام التعليمي الشامل - النوافذ المنفصلة")
    app.setApplicationVersion("3.0")
    app.setOrganizationName("Modern Education Systems")

    # إنشاء النافذة الرئيسية
    window = MainSystemWindow()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == '__main__':
    print("🌐 بدء تشغيل النظام التعليمي الشامل - النوافذ المنفصلة...")
    print("=" * 70)
    print("📋 الميزات الجديدة:")
    print("   🔹 نافذة رئيسية بسيطة وأنيقة")
    print("   🔹 فتح النوافذ المنفصلة حسب الحاجة")
    print("   🔹 أداء ممتاز وذاكرة أقل")
    print("   🔹 سهولة الصيانة والتطوير")
    print("   🔹 واجهة HTML جميلة ومتجاوبة")
    print("=" * 70)
    print("🎯 الوحدات المتاحة:")
    print("   📥 نافذة الاستيراد")
    print("   ⚙️ نافذة الإعدادات")
    print("   🏢 نافذة بيانات المؤسسة")
    print("   📊 نافذة الإحصائيات (قيد التطوير)")
    print("=" * 70)
    print("🚀 جاري تشغيل النظام...")

    try:
        main()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("💡 تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5 PyQtWebEngine")
