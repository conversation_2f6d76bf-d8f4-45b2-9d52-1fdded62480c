#!/usr/bin/env python3
"""
اختبار بسيط للنظام الرئيسي - بدون GUI
"""

import sys
import os
import json

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_main_system_engine():
    """اختبار محرك النظام الرئيسي"""
    try:
        from main_system_with_separate_windows import MainSystemEngine
        
        print("✅ تم استيراد MainSystemEngine بنجاح")
        
        # إنشاء المحرك
        engine = MainSystemEngine()
        print("✅ تم إنشاء المحرك بنجاح")
        
        # اختبار الدوال
        print("\n🔧 اختبار الدوال المتاحة:")
        
        # اختبار getSystemStatus
        try:
            status = engine.getSystemStatus()
            status_dict = json.loads(status)
            print(f"✅ getSystemStatus يعمل: {len(status_dict)} عناصر")
        except Exception as e:
            print(f"⚠️ getSystemStatus خطأ: {e}")
        
        # اختبار getDatabaseStatistics
        try:
            stats = engine.getDatabaseStatistics()
            stats_dict = json.loads(stats)
            print(f"✅ getDatabaseStatistics يعمل: {len(stats_dict)} عناصر")
            print(f"   الطلاب: {stats_dict.get('students_count', 0)}")
            print(f"   الأساتذة: {stats_dict.get('teachers_count', 0)}")
            print(f"   السنة: {stats_dict.get('current_year', 'غير محدد')}")
        except Exception as e:
            print(f"⚠️ getDatabaseStatistics خطأ: {e}")
        
        # التحقق من وجود الدوال المطلوبة
        required_methods = [
            'openImportWindow',
            'openSettingsWindow', 
            'openInstitutionWindow',
            'openStatisticsWindow',
            'openPrinterSettingsWindow',
            'openDataEditWindow',
            'openSectionsManagementWindow',
            'showAbout'
        ]
        
        print("\n🎯 الدوال المطلوبة:")
        for method in required_methods:
            if hasattr(engine, method):
                print(f"✅ {method}")
            else:
                print(f"❌ {method} مفقودة!")
        
        print("\n🎯 النظام جاهز!")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == '__main__':
    print("🔧 اختبار محرك النظام الرئيسي...")
    print("=" * 50)
    
    success = test_main_system_engine()
    
    print("=" * 50)
    if success:
        print("✅ جميع الاختبارات نجحت!")
        print("💡 يمكنك الآن تشغيل main_system_with_separate_windows.py بثقة")
    else:
        print("❌ بعض الاختبارات فشلت!")
