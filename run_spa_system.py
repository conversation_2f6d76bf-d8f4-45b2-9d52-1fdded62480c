"""
النظام التعليمي الشامل الحديث - ملف واحد شامل
نظام الصفحات الموحد مثل صفحات الإنترنت الحديثة

الميزات:
- ملف واحد فقط - لا حاجة لملفات أخرى
- نظام صفحات موحد مثل المواقع الحديثة
- تنقل سلس بين الصفحات
- شريط تنقل علوي ثابت
- بدون تموجات أو تأثيرات متحركة
- أداء محسن ومستقر
"""

import sys
import os
import json
import sqlite3
import requests
from datetime import datetime
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QFileDialog, QHBoxLayout
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, QTimer, QDateTime, Qt
from PyQt5.QtGui import QIcon

# محاولة استيراد pandas مع معالجة الخطأ
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

# ملاحظة: تم إزالة نظام الاستيراد المدمج - سيعمل بالنوافذ المنفصلة
IMPORT_ENGINE_AVAILABLE = False

# استيراد محرك الإعدادات
try:
    from settings_engine import SettingsEngine
    SETTINGS_ENGINE_AVAILABLE = True
    print("✅ تم استيراد محرك الإعدادات (settings_engine.py) بنجاح")
except ImportError as e:
    SETTINGS_ENGINE_AVAILABLE = False
    print(f"❌ خطأ في استيراد محرك الإعدادات: {str(e)}")
    print("💡 تأكد من وجود ملف settings_engine.py في نفس مجلد البرنامج")
except Exception as e:
    SETTINGS_ENGINE_AVAILABLE = False
    print(f"❌ خطأ غير متوقع في استيراد محرك الإعدادات: {str(e)}")

# استيراد محرك المؤسسة
try:
    from institution_engine import InstitutionEngine
    INSTITUTION_ENGINE_AVAILABLE = True
    print("✅ تم استيراد محرك المؤسسة (institution_engine.py) بنجاح")
except ImportError as e:
    INSTITUTION_ENGINE_AVAILABLE = False
    print(f"❌ خطأ في استيراد محرك المؤسسة: {str(e)}")
    print("💡 تأكد من وجود ملف institution_engine.py في نفس مجلد البرنامج")
except Exception as e:
    INSTITUTION_ENGINE_AVAILABLE = False
    print(f"❌ خطأ غير متوقع في استيراد محرك المؤسسة: {str(e)}")

class SystemEngine(QObject):
    """محرك النظام الرئيسي - مسؤول عن إدارة النظام والصفحات"""

    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    systemStatusUpdated = pyqtSignal(str)  # system status JSON
    pageChanged = pyqtSignal(str)  # page_name
    serverStatusUpdated = pyqtSignal(bool, str)  # is_connected, info

    def __init__(self):
        super().__init__()
        self.db_path = "data.db"
        self.server_url = "http://localhost:5000"
        self.current_page = "home"
        self.main_window = None  # مرجع للنافذة الرئيسية
        self.settings_window = None  # مرجع لنافذة الإعدادات
        
        # إنشاء محرك الاستيراد إذا كان متوفراً
        self.import_engine = None
        if IMPORT_ENGINE_AVAILABLE:
            # تم إزالة النظام المدمج - سيعمل بالنوافذ المنفصلة
            pass
        
        # إنشاء محرك الإعدادات إذا كان متوفراً
        self.settings_engine = None
        if SETTINGS_ENGINE_AVAILABLE:
            try:
                self.settings_engine = SettingsEngine(parent_window=self.main_window)
                # ربط الإشارات
                self.settings_engine.logUpdated.connect(self.logUpdated)
                self.settings_engine.operationProgressUpdated.connect(self.import_progress_updated)
                print("✅ تم إنشاء محرك الإعدادات بنجاح")
            except Exception as e:
                print(f"❌ خطأ في إنشاء محرك الإعدادات: {str(e)}")
                self.settings_engine = None

        # إنشاء محرك المؤسسة إذا كان متوفراً
        self.institution_engine = None
        if INSTITUTION_ENGINE_AVAILABLE:
            try:
                self.institution_engine = InstitutionEngine(parent_window=self.main_window)
                # ربط الإشارات
                self.institution_engine.logUpdated.connect(self.logUpdated)
                self.institution_engine.dataUpdated.connect(self.institution_data_updated)
                self.institution_engine.logoUpdated.connect(self.institution_logo_updated)
                print("✅ تم إنشاء محرك المؤسسة بنجاح")
            except Exception as e:
                print(f"❌ خطأ في إنشاء محرك المؤسسة: {str(e)}")
                self.institution_engine = None

        # مؤقت لفحص حالة الخادم
        self.server_check_timer = QTimer()
        self.server_check_timer.timeout.connect(self.check_server_status)
        self.server_check_timer.start(30000)  # فحص كل 30 ثانية

    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.logUpdated.emit(message, status, timestamp)

    def import_progress_updated(self, percentage, status_text):
        """تحديث شريط التقدم للاستيراد"""
        # يمكن إرسال هذا للواجهة إذا لزم الأمر
        self.emit_log(f"التقدم: {percentage}% - {status_text}", "progress")

    def institution_data_updated(self, data_json):
        """تحديث بيانات المؤسسة"""
        self.emit_log("تم تحديث بيانات المؤسسة", "info")

    def institution_logo_updated(self, logo_path):
        """تحديث شعار المؤسسة"""
        self.emit_log(f"تم تحديث شعار المؤسسة: {logo_path}", "info")

    @pyqtSlot(result=str)
    def getSystemStatus(self):
        """الحصول على حالة النظام الشاملة"""
        try:
            status = {
                "database": {
                    "exists": os.path.exists(self.db_path),
                    "size_mb": self.get_file_size(self.db_path) if os.path.exists(self.db_path) else 0,
                    "tables_count": self.get_tables_count()
                },
                "modules": {
                    "import_system": IMPORT_ENGINE_AVAILABLE,
                    "settings_system": SETTINGS_ENGINE_AVAILABLE,
                    "institution_system": INSTITUTION_ENGINE_AVAILABLE,
                    "statistics_system": True,
                    "documents_system": True
                },
                "server": {
                    "status": "checking",
                    "url": self.server_url
                },
                "system_info": {
                    "version": "2.0 - ملف واحد شامل",
                    "last_update": "يونيو 2025",
                    "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
                }
            }
            return json.dumps(status, ensure_ascii=False)
        except Exception as e:
            self.emit_log(f"خطأ في جمع حالة النظام: {str(e)}", "error")
            return json.dumps({"error": str(e)}, ensure_ascii=False)

    def get_file_size(self, file_path):
        """الحصول على حجم الملف بالميجابايت"""
        try:
            size_bytes = os.path.getsize(file_path)
            return round(size_bytes / (1024 * 1024), 2)
        except:
            return 0

    def get_tables_count(self):
        """عدد الجداول في قاعدة البيانات"""
        try:
            if not os.path.exists(self.db_path):
                return 0

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except:
            return 0

    @pyqtSlot(str)
    def navigateToPage(self, page_name):
        """التنقل إلى صفحة معينة"""
        self.current_page = page_name
        self.emit_log(f"الانتقال إلى صفحة: {page_name}", "info")
        self.pageChanged.emit(page_name)

    @pyqtSlot(result=str)
    def getCurrentPage(self):
        """الحصول على الصفحة الحالية"""
        return self.current_page

    @pyqtSlot()
    def openImportSystem(self):
        """فتح نظام الاستيراد - نافذة منفصلة"""
        self.emit_log("🔄 فتح نظام الاستيراد في نافذة منفصلة...", "info")
        
        try:
            # استيراد وفتح نافذة الاستيراد المنفصلة
            from import_engine_corrected import ImportEngine
            from PyQt5.QtWidgets import QDialog
            
            # إنشاء نافذة استيراد منفصلة
            import_window = ImportEngine(parent_window=self.main_window)
            if hasattr(import_window, 'show'):
                import_window.show()
                self.emit_log("✅ تم فتح نافذة الاستيراد بنجاح", "success")
            else:
                self.emit_log("❌ خطأ: نافذة الاستيراد لا تحتوي على دالة عرض", "error")
                
        except ImportError:
            self.emit_log("❌ خطأ: ملف import_engine_corrected.py غير موجود", "error")
        except Exception as e:
            self.emit_log(f"❌ خطأ في فتح نافذة الاستيراد: {str(e)}", "error")

    @pyqtSlot()
    def closeImportSystem(self):
        """إغلاق نظام الاستيراد"""
        # العودة للصفحة الرئيسية
        self.navigateToPage("home")

    @pyqtSlot()
    def selectMasarFile(self):
        """اختيار ملف لوائح منظومة مسار - نافذة منفصلة"""
        self.emit_log("❌ نظام الاستيراد المدمج غير متوفر - استخدم النافذة المنفصلة", "warning")

    @pyqtSlot()
    def selectTeachersFile(self):
        """اختيار ملف أسماء الأساتذة - نافذة منفصلة"""
        self.emit_log("❌ نظام الاستيراد المدمج غير متوفر - استخدم النافذة المنفصلة", "warning")

    @pyqtSlot()
    def selectSecretCodesFiles(self):
        """اختيار ملفات الرموز السرية - نافذة منفصلة"""
        self.emit_log("❌ نظام الاستيراد المدمج غير متوفر - استخدم النافذة المنفصلة", "warning")

    @pyqtSlot()
    def refreshImportData(self):
        """تحديث بيانات الاستيراد - نافذة منفصلة"""
        self.emit_log("❌ نظام الاستيراد المدمج غير متوفر - استخدم النافذة المنفصلة", "warning")

    @pyqtSlot(result=bool)
    def checkPandasAvailability(self):
        """التحقق من توفر pandas - بدون محرك مدمج"""
        return PANDAS_AVAILABLE

    # =============== دوال محرك المؤسسة ===============
    @pyqtSlot()
    def openInstitutionSystem(self):
        """فتح نظام بيانات المؤسسة"""
        self.emit_log("🔄 تم طلب فتح نظام بيانات المؤسسة...", "info")
        
        if not INSTITUTION_ENGINE_AVAILABLE:
            self.emit_log("❌ خطأ: محرك المؤسسة غير متوفر أو يحتوي على أخطاء", "error")
            return
            
        if self.main_window:
            # الانتقال إلى صفحة بيانات المؤسسة في النظام
            self.navigateToPage("institution")
        else:
            self.emit_log("❌ خطأ: النافذة الرئيسية غير متوفرة", "error")

    @pyqtSlot(result=str)
    def getInstitutionData(self):
        """الحصول على بيانات المؤسسة"""
        if self.institution_engine:
            return self.institution_engine.getInstitutionData()
        return json.dumps({"error": "محرك المؤسسة غير متوفر"}, ensure_ascii=False)

    @pyqtSlot(str, result=str)
    def saveInstitutionData(self, dataJson):
        """حفظ بيانات المؤسسة"""
        if self.institution_engine:
            return self.institution_engine.saveInstitutionData(dataJson)
        return json.dumps({"success": False, "message": "محرك المؤسسة غير متوفر"}, ensure_ascii=False)

    @pyqtSlot()
    def selectInstitutionLogo(self):
        """اختيار شعار المؤسسة"""
        if self.institution_engine:
            self.institution_engine.selectLogo()
        else:
            self.emit_log("❌ محرك المؤسسة غير متوفر", "error")

    @pyqtSlot(result=str)
    def getInstitutionCode(self):
        """الحصول على رمز المؤسسة"""
        if self.institution_engine:
            return self.institution_engine.getInstitutionCode()
        return json.dumps({"code": "غير متوفر", "error": "محرك المؤسسة غير متوفر"}, ensure_ascii=False)

    @pyqtSlot(result=str)
    def getInstitutionInfo(self):
        """الحصول على معلومات المؤسسة للعرض"""
        if self.institution_engine:
            return self.institution_engine.getInstitutionInfo()
        return json.dumps({"success": False, "message": "محرك المؤسسة غير متوفر"}, ensure_ascii=False)

    @pyqtSlot()
    def refreshInstitutionData(self):
        """تحديث بيانات المؤسسة"""
        if self.institution_engine:
            self.institution_engine.refreshData()
        else:
            self.emit_log("❌ محرك المؤسسة غير متوفر", "error")

    # =============== دوال محرك الإعدادات ===============
    @pyqtSlot()
    def openSettingsSystem(self):
        """فتح نظام الإعدادات"""
        self.emit_log("🔄 تم طلب فتح نظام الإعدادات...", "info")
        
        if not SETTINGS_ENGINE_AVAILABLE:
            self.emit_log("❌ خطأ: محرك الإعدادات غير متوفر أو يحتوي على أخطاء", "error")
            return
            
        if self.main_window:
            self.emit_log("✅ جاري الانتقال إلى صفحة الإعدادات...", "info")
            # الانتقال إلى صفحة الإعدادات بدلاً من فتح نافذة منفصلة
            self.navigateToPage("settings")
        else:
            self.emit_log("❌ خطأ: النافذة الرئيسية غير متوفرة", "error")

    @pyqtSlot()
    def check_server_status(self):
        """فحص حالة الخادم"""
        try:
            response = requests.get(f"{self.server_url}/api/check-status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                server_info = {
                    "connected": True,
                    "url": self.server_url,
                    "database_file": data.get("database_file", "data.db"),
                    "total_tables": data.get("total_tables", 0),
                    "status": "متصل ويعمل بشكل طبيعي"
                }
                self.serverStatusUpdated.emit(True, json.dumps(server_info, ensure_ascii=False))
            else:
                raise Exception("Server returned non-200 status")
        except Exception as e:
            server_info = {
                "connected": False,
                "url": self.server_url,
                "error": str(e),
                "status": "غير متصل"
            }
            self.serverStatusUpdated.emit(False, json.dumps(server_info, ensure_ascii=False))

    @pyqtSlot()
    def showHelpGuide(self):
        """عرض دليل المساعدة"""
        help_text = """
📖 دليل استخدام نظام الاستيراد:

📚 استيراد اللوائح من منظومة مسار:
- اختر ملف Excel يحتوي على بيانات الطلاب
- تأكد أن الملف بصيغة .xlsx أو .xls
- يجب أن يحتوي على الأعمدة المطلوبة

🔐 استيراد الرموز السرية:
- يمكن اختيار ملفات متعددة
- كل ملف يجب أن يحتوي على عمودين: الرمز والرمز السري

👨‍🏫 استيراد الأساتذة:
- ملف Excel يحتوي على: المادة، الرمز، اسم الأستاذ
- يتم تجميع البيانات حسب المادة تلقائياً

🔄 الوافدين والمغادرين:
- ملف Excel لتحديث بيانات الطلاب الجدد أو المنقولين
        """
        self.emit_log(help_text, "info")
        """عرض مساعدة النظام"""
        help_text = """
🎓 النظام التعليمي الشامل - المساعدة (ملف واحد شامل)

📥 نظام استيراد البيانات:
- استيراد اللوائح من منظومة مسار
- استيراد الرموز السرية دفعة واحدة
- استيراد أسماء الأساتذة والمواد

⚙️ إعدادات النظام:
- النسخ الاحتياطي والاستعادة
- تنظيف قاعدة البيانات
- تهيئة موسم دراسي جديد

✨ مميزات هذه النسخة:
- ملف واحد شامل - لا حاجة لملفات أخرى
- تنقل سلس مثل صفحات الإنترنت
- شريط تنقل علوي ثابت
- أداء محسن بدون تموجات
        """
        self.emit_log(help_text, "info")

    @pyqtSlot(result=str)
    def getDatabaseStatistics(self):
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            if not os.path.exists(self.db_path):
                return json.dumps({
                    "students_count": 0,
                    "teachers_count": 0,
                    "sections_count": 0,
                    "levels_count": 0,
                    "secret_codes_count": 0,
                    "current_year": "غير محدد"
                }, ensure_ascii=False)

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            stats = {}

            # السنة الدراسية الحالية
            try:
                cursor.execute("SELECT DISTINCT السنة_الدراسية FROM اللوائح ORDER BY السنة_الدراسية DESC LIMIT 1")
                result = cursor.fetchone()
                stats['current_year'] = result[0] if result and result[0] else "غير محدد"
            except:
                stats['current_year'] = "غير محدد"

            # إحصائيات الطلاب
            try:
                cursor.execute("SELECT COUNT(*) FROM اللوائح")
                stats['students_count'] = cursor.fetchone()[0]
            except:
                stats['students_count'] = 0

            # إحصائيات الأساتذة
            try:
                cursor.execute("SELECT COUNT(*) FROM الأساتذة")
                stats['teachers_count'] = cursor.fetchone()[0]
            except:
                stats['teachers_count'] = 0

            # إحصائيات الأقسام
            try:
                cursor.execute("SELECT COUNT(DISTINCT القسم) FROM اللوائح")
                stats['sections_count'] = cursor.fetchone()[0]
            except:
                stats['sections_count'] = 0

            # إحصائيات المستويات
            try:
                cursor.execute("SELECT COUNT(DISTINCT المستوى) FROM اللوائح")
                stats['levels_count'] = cursor.fetchone()[0]
            except:
                stats['levels_count'] = 0

            # إحصائيات الرموز السرية
            try:
                cursor.execute("SELECT COUNT(*) FROM الرمز_السري")
                stats['secret_codes_count'] = cursor.fetchone()[0]
            except:
                stats['secret_codes_count'] = 0

            conn.close()
            return json.dumps(stats, ensure_ascii=False)

        except Exception as e:
            return json.dumps({
                "error": str(e),
                "students_count": 0,
                "teachers_count": 0,
                "sections_count": 0,
                "levels_count": 0,
                "secret_codes_count": 0,
                "current_year": "غير محدد"
            }, ensure_ascii=False)

class ModernSPASystem(QMainWindow):
    """النظام الرئيسي الحديث - نظام الصفحات الموحد (ملف واحد شامل)"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎓 النظام التعليمي الشامل - ملف واحد شامل")
        self.setGeometry(100, 100, 1200, 800)

        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء محرك النظام
        self.system_engine = SystemEngine()
        self.system_engine.main_window = self  # ربط النافذة الرئيسية
        
        # تحديث parent_window في محرك الاستيراد (تم إزالة النظام المدمج)
        # if self.system_engine.import_engine:
        #     self.system_engine.import_engine.parent_window = self
        
        # تحديث parent_window في محرك الإعدادات
        if self.system_engine.settings_engine:
            self.system_engine.settings_engine.parent_window = self

        # إعداد الواجهة
        self.setup_ui()
        self.setup_web_channel()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء عرض الويب الموحد
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)

        # تحميل واجهة HTML الموحدة
        html_content = self.get_complete_html()
        self.web_view.setHtml(html_content)

    def setup_web_channel(self):
        """إعداد قناة التواصل مع JavaScript"""
        self.channel = QWebChannel()
        self.channel.registerObject("systemEngine", self.system_engine)
        
        # تسجيل محرك الاستيراد إذا كان متوفراً (تم إزالة النظام المدمج)
        # if self.system_engine.import_engine:
        #     self.channel.registerObject("importEngine", self.system_engine.import_engine)
        #     print("✅ تم تسجيل محرك الاستيراد مع QWebChannel")
        
        # تسجيل محرك الإعدادات إذا كان متوفراً
        if self.system_engine.settings_engine:
            self.channel.registerObject("settingsEngine", self.system_engine.settings_engine)
            print("✅ تم تسجيل محرك الإعدادات مع QWebChannel")
        
        # تسجيل محرك المؤسسة إذا كان متوفراً
        if self.system_engine.institution_engine:
            self.channel.registerObject("institutionEngine", self.system_engine.institution_engine)
            print("✅ تم تسجيل محرك المؤسسة مع QWebChannel")
        
        self.web_view.page().setWebChannel(self.channel)

        # انتظار تحميل الصفحة قبل إعداد القناة
        self.web_view.loadFinished.connect(self.on_page_loaded)

    def on_page_loaded(self):
        """استدعاء عند انتهاء تحميل الصفحة"""
        # إعادة تسجيل الكائن للتأكد من الربط
        self.channel.registerObject("systemEngine", self.system_engine)
        
        # إعادة تسجيل محرك الاستيراد (تم إزالة النظام المدمج)
        # if self.system_engine.import_engine:
        #     self.channel.registerObject("importEngine", self.system_engine.import_engine)
        #     print("✅ إعادة تسجيل محرك الاستيراد مع QWebChannel")
        
        # إعادة تسجيل محرك الإعدادات
        if self.system_engine.settings_engine:
            self.channel.registerObject("settingsEngine", self.system_engine.settings_engine)
            print("✅ إعادة تسجيل محرك الإعدادات مع QWebChannel")
        
        # إعادة تسجيل محرك المؤسسة
        if self.system_engine.institution_engine:
            self.channel.registerObject("institutionEngine", self.system_engine.institution_engine)
            print("✅ إعادة تسجيل محرك المؤسسة مع QWebChannel")
    
        def get_complete_html(self):
            """HTML كامل مع CSS و JavaScript - كل شيء في ملف واحد"""
            # قراءة محتوى ملف CSS
        css_content = ""
        try:
            with open('modern_system_styles.css', 'r', encoding='utf-8') as f:
                css_content = f.read()
        except FileNotFoundError:
            print("⚠️ لم يتم العثور على ملف modern_system_styles.css")
        except Exception as e:
            print(f"⚠️ خطأ في قراءة ملف CSS: {e}")
        
        return f"""<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النظام التعليمي الشامل - ملف واحد شامل</title>
    <style>
{css_content}
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar">
        <div class="navbar-container">
            <div class="navbar-brand">
                🎓 النظام التعليمي الشامل
            </div>
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link active" onclick="navigateToPage('home')" data-page="home">🏠 الرئيسية</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" onclick="navigateToPage('import')" data-page="import">📥 الاستيراد</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" onclick="navigateToPage('settings')" data-page="settings">⚙️ الإعدادات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" onclick="navigateToPage('institution')" data-page="institution">🏢 المؤسسة</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" onclick="navigateToPage('statistics')" data-page="statistics">📊 الإحصائيات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" onclick="navigateToPage('documents')" data-page="documents">📝 المستندات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" onclick="navigateToPage('help')" data-page="help">❓ المساعدة</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- شريط الحالة -->
    <div class="status-bar">
        <div class="status-indicator">
            <div class="status-dot"></div>
            <span>النظام متصل ويعمل بشكل طبيعي</span>
        </div>
        <span id="current-time">جاري التحميل...</span>
    </div>

    <!-- منطقة المحتوى الرئيسية -->
    <main class="main-content">
        <!-- صفحة الرئيسية -->
        <div id="home-page" class="page active">
            <div class="welcome-section">
                <h1>مرحباً بك في النظام التعليمي الشامل</h1>
                <p>منظومة متكاملة لإدارة البيانات التعليمية والطلابية - ملف واحد شامل</p>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="studentsCount">0</div>
                    <div class="stat-label">الطلاب المسجلين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="teachersCount">0</div>
                    <div class="stat-label">أعضاء هيئة التدريس</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="sectionsCount">0</div>
                    <div class="stat-label">الأقسام الأكاديمية</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="secretCodesCount">0</div>
                    <div class="stat-label">الرموز السرية</div>
                </div>
            </div>

            <!-- بطاقات الوحدات -->
            <div class="modules-grid">
                <div class="module-card import-module" onclick="loadImportSystem()">
                    <span class="module-icon">📥</span>
                    <div class="module-title">نظام الاستيراد</div>
                    <div class="module-description">استيراد البيانات من ملفات Excel ومنظومة مسار</div>
                    <div class="module-status">جاهز للاستخدام</div>
                </div>
                <div class="module-card" onclick="navigateToPage('settings')">
                    <span class="module-icon">⚙️</span>
                    <div class="module-title">إعدادات النظام</div>
                    <div class="module-description">النسخ الاحتياطي والصيانة وإعدادات النظام</div>
                </div>
                <div class="module-card" onclick="navigateToPage('institution')">
                    <span class="module-icon">🏢</span>
                    <div class="module-title">بيانات المؤسسة</div>
                    <div class="module-description">إدارة معلومات المؤسسة والأقسام الأكاديمية</div>
                </div>
                <div class="module-card" onclick="navigateToPage('statistics')">
                    <span class="module-icon">📊</span>
                    <div class="module-title">الإحصائيات والتقارير</div>
                    <div class="module-description">تقارير شاملة وإحصائيات تفصيلية</div>
                </div>
                <div class="module-card" onclick="navigateToPage('documents')">
                    <span class="module-icon">📝</span>
                    <div class="module-title">إدارة المستندات</div>
                    <div class="module-description">تنظيم وإدارة الأوراق والملاحظات</div>
                </div>
            </div>

            <!-- معلومات النظام -->
            <div class="highlight">
                <h3>🎉 ملف واحد شامل!</h3>
                <p>لا حاجة لملفات أخرى - كل شيء في مكان واحد</p>
            </div>
        </div>

        <!-- صفحة نظام الاستيراد -->
        <div id="import-page" class="page">
            <div class="module-page">
                <div class="page-header">
                    <h2>📥 نظام الاستيراد</h2>
                    <p>استيراد البيانات من ملفات Excel ومنظومة مسار</p>
                </div>

                <!-- أزرار الاستيراد -->
                <div class="import-buttons-grid">
                    <button class="import-action-button" onclick="importMasarData()">
                        📚 استيراد اللوائح من منظومة مسار
                    </button>
                    <button class="import-action-button" onclick="importSecretCodes()">
                        🔐 استيراد الرمز السري دفعة واحدة
                    </button>
                    <button class="import-action-button" onclick="importTeachers()">
                        👨‍🏫 استيراد أسماء الأساتذة والمواد
                    </button>
                    <button class="import-action-button" onclick="refreshImportData()">
                        🔄 تحديث البيانات والإحصائيات
                    </button>
                </div>

                <!-- شريط التحميل -->
                <div class="import-progress-container" id="importProgressContainer">
                    <div class="import-progress-text" id="importProgressText">جاهز للاستخدام</div>
                    <div class="import-progress-bar">
                        <div class="import-progress-fill" id="importProgressFill" style="width: 0%;"></div>
                    </div>
                    <div class="import-progress-percentage" id="importProgressPercentage">0%</div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="import-stats-container">
                    <div class="import-stat-item academic-year">
                        <div class="import-stat-value" id="importCurrentYear">غير محدد</div>
                        <div class="import-stat-label">السنة الدراسية</div>
                    </div>
                    <div class="import-stat-item">
                        <div class="import-stat-value" id="importLevelsCount">0</div>
                        <div class="import-stat-label">عدد المستويات</div>
                    </div>
                    <div class="import-stat-item">
                        <div class="import-stat-value" id="importSectionsCount">0</div>
                        <div class="import-stat-label">عدد الأقسام</div>
                    </div>
                    <div class="import-stat-item">
                        <div class="import-stat-value" id="importStudentsCount">0</div>
                        <div class="import-stat-label">عدد التلاميذ</div>
                    </div>
                    <div class="import-stat-item">
                        <div class="import-stat-value" id="importTeachersCount">0</div>
                        <div class="import-stat-label">الأساتذة</div>
                    </div>
                </div>

                <!-- معلومات النظام -->
                <div class="info-box">
                    <h4>� الوظائف المتاحة:</h4>
                    <p>• استيراد اللوائح من منظومة مسار</p>
                    <p>• استيراد الرموز السرية دفعة واحدة</p>
                    <p>• استيراد أسماء الأساتذة والمواد</p>
                    <p>• معالجة ملفات Excel المختلفة</p>
                    <div id="importSystemStatus" style="margin-top: 15px; padding: 10px; border-radius: 5px; background: #e8f5e8; border: 1px solid #4caf50;">
                        <strong>حالة النظام:</strong> <span id="systemStatusText">جاري التحقق...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- صفحة الإعدادات -->
        <div id="settings-page" class="page">
            <div class="module-page">
                <div class="page-header">
                    <h2>⚙️ إعدادات النظام</h2>
                    <p>النسخ الاحتياطي والصيانة وإعدادات النظام</p>
                </div>
                
                <!-- شريط التقدم للإعدادات -->
                <div id="settingsProgressContainer" style="display: none; margin-bottom: 20px;">
                    <div class="progress-bar">
                        <div id="settingsProgressBar" class="progress-fill"></div>
                    </div>
                    <div id="settingsProgressText">جاري المعالجة...</div>
                </div>
                
                <!-- إحصائيات قاعدة البيانات -->
                <div class="settings-section">
                    <h3>📊 معلومات قاعدة البيانات</h3>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <span class="stat-number" id="dbSize">0 MB</span>
                            <span class="stat-label">حجم قاعدة البيانات</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-number" id="dbTables">0</span>
                            <span class="stat-label">عدد الجداول</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-number" id="dbLastModified">غير معروف</span>
                            <span class="stat-label">آخر تعديل</span>
                        </div>
                    </div>
                </div>
                
                <!-- أزرار الإعدادات الرئيسية -->
                <div class="settings-section">
                    <h3>🛠️ الصيانة والنسخ الاحتياطي</h3>
                    <div class="settings-buttons">
                        <button class="btn btn-warning" onclick="deleteAllDataWithPassword()">
                            🗑️ حذف جميع البيانات
                        </button>
                        <button class="btn btn-info" onclick="resetSchoolYearWithPassword()">
                            🔄 تهيئة سنة دراسية جديدة
                        </button>
                        <button class="btn btn-success" onclick="backupDatabase()">
                            💾 نسخة احتياطية
                        </button>
                        <button class="btn btn-primary" onclick="insertWeeklyAbsence()">
                            📝 إدراج الغياب الأسبوعي
                        </button>
                    </div>
                </div>
                
                <!-- حالة المكتبات -->
                <div class="settings-section">
                    <h3>📚 حالة المكتبات</h3>
                    <div id="librariesStatus">
                        <p>جاري تحميل معلومات المكتبات...</p>
                    </div>
                    <button class="btn btn-secondary" onclick="showLibrariesStatus()">
                        🔍 عرض تفاصيل المكتبات
                    </button>
                </div>
                
                <!-- العودة للرئيسية -->
                <div style="text-align: center; margin-top: 30px;">
                    <button class="btn btn-secondary" onclick="navigateToPage('home')">🏠 العودة للرئيسية</button>
                </div>
            </div>
        </div>

        <!-- صفحة بيانات المؤسسة -->
        <div id="institution-page" class="page">
            <div class="module-page">
                <div class="page-header">
                    <h2>🏢 بيانات المؤسسة</h2>
                    <p>إدارة معلومات المؤسسة والأقسام الأكاديمية</p>
                </div>
                <div style="text-align: center; padding: 50px;">
                    <span style="font-size: 80px;">🚧</span>
                    <h3 style="color: #6c757d; margin: 20px 0;">قيد التطوير</h3>
                    <p style="color: #6c757d;">سيتم إضافة هذه الوحدة في التحديثات القادمة</p>
                    <button class="btn btn-secondary" onclick="navigateToPage('home')">العودة للرئيسية</button>
                </div>
            </div>
        </div>

        <!-- صفحة بيانات المؤسسة -->
        <div id="institution-page" class="page">
            <div class="module-page">
                <div class="page-header">
                    <h2>🏢 بيانات المؤسسة</h2>
                    <p>إدارة معلومات المؤسسة والشعار والبيانات الأساسية</p>
                </div>
                
                <!-- شريط التقدم -->
                <div id="institutionProgressContainer" style="display: none; margin-bottom: 20px;">
                    <div class="progress-bar">
                        <div id="institutionProgressBar" class="progress-fill"></div>
                    </div>
                    <div id="institutionProgressText">جاري المعالجة...</div>
                </div>
                
                <!-- شعار المؤسسة -->
                <div class="institution-section">
                    <h3>🖼️ شعار المؤسسة</h3>
                    <div class="logo-section">
                        <div id="institutionLogo" class="logo-display">
                            <span>لا يوجد شعار</span>
                        </div>
                        <div class="logo-controls">
                            <button class="btn btn-primary" onclick="selectInstitutionLogo()">📷 تحميل الشعار</button>
                            <button class="btn btn-info" onclick="showInstitutionCode()">🏷️ رمز المؤسسة</button>
                        </div>
                    </div>
                </div>
                
                <!-- نموذج بيانات المؤسسة -->
                <div class="institution-section">
                    <h3>📋 بيانات المؤسسة</h3>
                    <form id="institutionForm" class="institution-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label>الأكاديمية:</label>
                                <input type="text" id="academy" name="الأكاديمية" required>
                            </div>
                            <div class="form-group">
                                <label>المديرية:</label>
                                <input type="text" id="directorate" name="المديرية" required>
                            </div>
                            <div class="form-group">
                                <label>الجماعة:</label>
                                <input type="text" id="commune" name="الجماعة">
                            </div>
                            <div class="form-group">
                                <label>المؤسسة:</label>
                                <input type="text" id="school" name="المؤسسة" required>
                            </div>
                            <div class="form-group">
                                <label>السنة الدراسية:</label>
                                <select id="academicYear" name="السنة_الدراسية">
                                    <option value="">اختر السنة</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>البلدة:</label>
                                <input type="text" id="town" name="البلدة" required>
                            </div>
                            <div class="form-group">
                                <label>المدير:</label>
                                <select id="director" name="المدير">
                                    <option value="من مدير">من مدير</option>
                                    <option value="من مديرة">من مديرة</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>الحراسة العامة:</label>
                                <select id="guardian" name="الحارس_العام">
                                    <option value="من حارس عام">من حارس عام</option>
                                    <option value="من حارسة عامة">من حارسة عامة</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>السلك:</label>
                                <select id="level" name="السلك">
                                    <option value="التعليم الابتدائي">التعليم الابتدائي</option>
                                    <option value="الثانوي الإعدادي">الثانوي الإعدادي</option>
                                    <option value="الثانوي التأهيلي">الثانوي التأهيلي</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>رقم الحراسة:</label>
                                <select id="guardNumber" name="رقم_الحراسة">
                                    <option value="حراسة رقم 1">حراسة رقم 1</option>
                                    <option value="حراسة رقم 2">حراسة رقم 2</option>
                                    <option value="حراسة رقم 3">حراسة رقم 3</option>
                                    <option value="حراسة رقم 4">حراسة رقم 4</option>
                                    <option value="حراسة رقم 5">حراسة رقم 5</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>رقم التسجيل:</label>
                                <input type="text" id="registrationNumber" name="رقم_التسجيل" required>
                            </div>
                            <div class="form-group">
                                <label>الأسدس:</label>
                                <select id="semester" name="الأسدس">
                                    <option value="الأول">الأول</option>
                                    <option value="الثاني">الثاني</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- أزرار التحكم -->
                <div class="control-buttons">
                    <button class="btn btn-success" onclick="saveInstitutionData()">💾 حفظ البيانات</button>
                    <button class="btn btn-info" onclick="refreshInstitutionData()">🔄 تحديث البيانات</button>
                    <button class="btn btn-warning" onclick="showInstitutionInfo()">ℹ️ معلومات المؤسسة</button>
                    <button class="btn btn-secondary" onclick="navigateToPage('home')">🏠 العودة للرئيسية</button>
                </div>
            </div>
        </div>

        <!-- صفحة الإحصائيات -->
        <div id="statistics-page" class="page">
            <div class="module-page">
                <div class="page-header">
                    <h2>📊 الإحصائيات والتقارير</h2>
                    <p>تقارير شاملة وإحصائيات تفصيلية</p>
                </div>
                <div style="text-align: center; padding: 50px;">
                    <span style="font-size: 80px;">🚧</span>
                    <h3 style="color: #6c757d; margin: 20px 0;">قيد التطوير</h3>
                    <p style="color: #6c757d;">سيتم إضافة هذه الوحدة في التحديثات القادمة</p>
                    <button class="btn btn-secondary" onclick="navigateToPage('home')">العودة للرئيسية</button>
                </div>
            </div>
        </div>

        <!-- صفحة المستندات -->
        <div id="documents-page" class="page">
            <div class="module-page">
                <div class="page-header">
                    <h2>📝 إدارة المستندات</h2>
                    <p>تنظيم وإدارة الأوراق والملاحظات</p>
                </div>
                <div style="text-align: center; padding: 50px;">
                    <span style="font-size: 80px;">🚧</span>
                    <h3 style="color: #6c757d; margin: 20px 0;">قيد التطوير</h3>
                    <p style="color: #6c757d;">سيتم إضافة هذه الوحدة في التحديثات القادمة</p>
                    <button class="btn btn-secondary" onclick="navigateToPage('home')">العودة للرئيسية</button>
                </div>
            </div>
        </div>

        <!-- صفحة المساعدة -->
        <div id="help-page" class="page">
            <div class="module-page">
                <div class="page-header">
                    <h2>❓ المساعدة والدعم</h2>
                    <p>دليل الاستخدام والمساعدة الفنية</p>
                </div>
                <div class="info-box">
                    <h4>🎯 كيفية الاستخدام:</h4>
                    <p>• استخدم شريط التنقل العلوي للانتقال بين الصفحات</p>
                    <p>• انقر على بطاقات الوحدات في الصفحة الرئيسية</p>
                    <p>• تابع الإحصائيات المحدثة في الصفحة الرئيسية</p>
                    <p>• استخدم أزرار التشغيل لتحميل الوحدات المتاحة</p>
                </div>

                <div class="info-box">
                    <h4>🔧 الوحدات المتاحة:</h4>
                    <p>• <strong>نظام الاستيراد:</strong> استيراد البيانات من ملفات Excel</p>
                    <p>• <strong>إعدادات النظام:</strong> النسخ الاحتياطي والصيانة</p>
                    <p>• <strong>بيانات المؤسسة:</strong> قيد التطوير</p>
                    <p>• <strong>الإحصائيات:</strong> قيد التطوير</p>
                    <p>• <strong>المستندات:</strong> قيد التطوير</p>
                </div>

                <div class="highlight">
                    <h4>✨ مميزات هذه النسخة:</h4>
                    <p>ملف واحد شامل - تنقل سلس - أداء محسن - بدون تموجات</p>
                    <button class="btn" onclick="showSystemHelp()">عرض المساعدة التفصيلية</button>
                </div>
            </div>
        </div>
    </main>

    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <script>
        let systemEngine = null;
        let importEngine = null;
        let settingsEngine = null;
        let institutionEngine = null;
        let isChannelReady = false;
        let currentPage = 'home';

        // إعداد قناة التواصل مع Python
        function initializeChannel() {
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    systemEngine = channel.objects.systemEngine;
                    importEngine = channel.objects.importEngine;
                    settingsEngine = channel.objects.settingsEngine;
                    institutionEngine = channel.objects.institutionEngine;
                    isChannelReady = true;

                    console.log('🚀 QWebChannel initialized successfully');

                    // ربط الإشارات
                    if (systemEngine) {
                        systemEngine.logUpdated.connect(addLogEntry);
                        systemEngine.systemStatusUpdated.connect(updateSystemStatus);
                        systemEngine.pageChanged.connect(onPageChanged);
                        systemEngine.serverStatusUpdated.connect(updateServerStatus);

                        // تحميل حالة النظام والإحصائيات
                        loadSystemStatus();
                        loadDatabaseStatistics();

                        // فحص حالة الخادم
                        systemEngine.check_server_status();

                        console.log('✅ تم تهيئة النظام بنجاح');
                    }

                    // ربط إشارات محرك الاستيراد
                    if (importEngine) {
                        importEngine.logUpdated.connect(addLogEntry);
                        importEngine.importProgressUpdated.connect(updateImportProgress);
                        importEngine.statisticsUpdated.connect(updateImportStatistics);
                        console.log('✅ تم تهيئة محرك الاستيراد بنجاح');
                    } else {
                        console.log('⚠️ محرك الاستيراد غير متوفر');
                    }
                });
            } else {
                console.log('⚠️ QWebChannel not available, retrying...');
                setTimeout(initializeChannel, 100);
            }
        }

        // التنقل بين الصفحات
        function navigateToPage(pageName) {
            // إخفاء جميع الصفحات
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => {
                page.classList.remove('active');
            });

            // إظهار الصفحة المطلوبة
            const targetPage = document.getElementById(pageName + '-page');
            if (targetPage) {
                targetPage.classList.add('active');
                currentPage = pageName;

                // تحديث شريط التنقل
                updateNavigation(pageName);

                // إشعار Python بالتغيير
                if (systemEngine) {
                    systemEngine.navigateToPage(pageName);
                }

                // معالجة خاصة لصفحات معينة
                if (pageName === 'settings') {
                    // تحميل معلومات قاعدة البيانات عند فتح صفحة الإعدادات
                    setTimeout(loadDatabaseInfo, 500);
                } else if (pageName === 'institution') {
                    // تهيئة صفحة المؤسسة عند فتحها
                    setTimeout(initializeInstitutionPage, 500);
                }

                console.log('📄 تم الانتقال إلى صفحة:', pageName);
            }
        }

        // تحديث شريط التنقل
        function updateNavigation(activePage) {
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('data-page') === activePage) {
                    link.classList.add('active');
                }
            });
        }

        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // تحميل حالة النظام
        function loadSystemStatus() {
            if (systemEngine) {
                try {
                    const statusResult = systemEngine.getSystemStatus();
                    console.log('Raw system status result:', statusResult);
                    
                    let systemStatus;
                    if (typeof statusResult === 'string') {
                        systemStatus = JSON.parse(statusResult);
                    } else if (typeof statusResult === 'object') {
                        systemStatus = statusResult;
                    } else {
                        console.error('Invalid system status result type:', typeof statusResult);
                        return;
                    }
                    
                    console.log('System Status:', systemStatus);
                    
                    // تحديث حالة نظام الاستيراد
                    const statusElement = document.getElementById('systemStatusText');
                    if (statusElement) {
                        if (systemStatus.modules && systemStatus.modules.import_system) {
                            statusElement.textContent = 'نظام الاستيراد متوفر وجاهز للاستخدام ✅';
                            statusElement.style.color = '#4caf50';
                        } else {
                            statusElement.textContent = 'نظام الاستيراد غير متوفر ❌';
                            statusElement.style.color = '#f44336';
                        }
                    }
                } catch (error) {
                    console.error('خطأ في تحميل حالة النظام:', error);
                    const statusElement = document.getElementById('systemStatusText');
                    if (statusElement) {
                        statusElement.textContent = 'خطأ في فحص حالة النظام ⚠️';
                        statusElement.style.color = '#ff9800';
                    }
                }
            }
        }

        // تحميل إحصائيات قاعدة البيانات
        function loadDatabaseStatistics() {
            if (systemEngine) {
                try {
                    // استخدام async/await للتعامل مع Promise
                    systemEngine.getDatabaseStatistics(function(statsResult) {
                        try {
                            console.log('Raw statistics result:', statsResult);
                            console.log('Type of statsResult:', typeof statsResult);
                            
                            if (!statsResult) {
                                console.error('Statistics result is null or undefined');
                                return;
                            }
                            
                            let stats;
                            if (typeof statsResult === 'string') {
                                stats = JSON.parse(statsResult);
                            } else if (typeof statsResult === 'object') {
                                stats = statsResult;
                            } else {
                                console.error('Invalid statistics result type:', typeof statsResult);
                                return;
                            }
                            
                            console.log('Parsed statistics:', stats);

                            document.getElementById('studentsCount').textContent = stats.students_count || 0;
                            document.getElementById('teachersCount').textContent = stats.teachers_count || 0;
                            document.getElementById('sectionsCount').textContent = stats.sections_count || 0;
                            document.getElementById('secretCodesCount').textContent = stats.secret_codes_count || 0;

                        } catch (error) {
                            console.error('خطأ في تحميل الإحصائيات:', error);
                            console.error('Error details:', error.message);
                            // تعيين قيم افتراضية في حالة الخطأ
                            document.getElementById('studentsCount').textContent = '0';
                            document.getElementById('teachersCount').textContent = '0';
                            document.getElementById('sectionsCount').textContent = '0';
                            document.getElementById('secretCodesCount').textContent = '0';
                        }
                    });

                } catch (error) {
                    console.error('خطأ في تحميل الإحصائيات:', error);
                    document.getElementById('studentsCount').textContent = 0;
                    document.getElementById('teachersCount').textContent = 0;
                    document.getElementById('sectionsCount').textContent = 0;
                    document.getElementById('secretCodesCount').textContent = 0;
                }
            }
        }

        // تحديث حالة الخادم
        function updateServerStatus(isConnected, serverInfo) {
            console.log('Server status updated:', isConnected, serverInfo);
        }

        // إضافة إدخال إلى السجل
        function addLogEntry(message, status, timestamp) {
            console.log(`[${timestamp}] ${status.toUpperCase()}: ${message}`);
        }

        // تحديث حالة النظام
        function updateSystemStatus(statusJson) {
            console.log('System status updated:', statusJson);
        }

        // استجابة لتغيير الصفحة من Python
        function onPageChanged(pageName) {
            console.log('Page changed from Python:', pageName);
        }

        // تحميل نظام الاستيراد
        function loadImportSystem() {
            if (systemEngine) {
                console.log('🔄 التنقل إلى صفحة الاستيراد...');
                addLogEntry('🔄 الانتقال إلى صفحة الاستيراد...', 'info', new Date().toLocaleTimeString());
                
                // الانتقال إلى صفحة الاستيراد بدلاً من فتح نافذة منفصلة
                navigateToPage('import');
                
                // تحميل محتوى صفحة الاستيراد التفاعلية
                loadImportPageContent();
            } else {
                console.error('❌ systemEngine غير متوفر');
                alert('❌ خطأ: النظام غير جاهز بعد\\nالرجاء المحاولة مرة أخرى بعد قليل');
            }
        }

        // تحميل محتوى صفحة الاستيراد التفاعلية
        function loadImportPageContent() {
            const importPage = document.getElementById('import-page');
            if (importPage) {
                // استبدال المحتوى الثابت بواجهة تفاعلية
                const interactiveContent = getImportPageContent();
                importPage.innerHTML = interactiveContent;
                
                // تهيئة وظائف الاستيراد
                initializeImportFunctions();
                
            }
        }

        // إنشاء محتوى صفحة الاستيراد التفاعلية
        function getImportPageContent() {
            return `
                <div class="module-page">
                    <div class="page-header">
                        <h2>📥 نظام الاستيراد التفاعلي</h2>
                        <p>استيراد البيانات من ملفات Excel ومنظومة مسار</p>
                    </div>

                    <!-- أزرار الاستيراد -->
                    <div class="import-buttons-grid">
                        <div class="import-action-card" onclick="importMasarData()">
                            <div class="action-icon">📚</div>
                            <h3>استيراد اللوائح من منظومة مسار</h3>
                            <p>استيراد بيانات الطلاب من ملفات Excel</p>
                        </div>
                        
                        <div class="import-action-card" onclick="importSecretCodes()">
                            <div class="action-icon">🔐</div>
                            <h3>استيراد الرموز السرية</h3>
                            <p>تحديث الرموز السرية دفعة واحدة</p>
                        </div>
                        
                        <div class="import-action-card" onclick="importTeachers()">
                            <div class="action-icon">👨‍🏫</div>
                            <h3>استيراد أسماء الأساتذة</h3>
                            <p>إضافة بيانات الأساتذة والمواد</p>
                        </div>
                        
                        <div class="import-action-card" onclick="importArrivals()">
                            <div class="action-icon">🔄</div>
                            <h3>الوافدين والمغادرين</h3>
                            <p>تحديث بيانات الطلاب الجدد</p>
                        </div>
                    </div>

                    <!-- شريط التقدم -->
                    <div class="progress-panel">
                        <h3>📊 حالة العمليات</h3>
                        <div class="progress-container-inline">
                            <div class="progress-bar-inline">
                                <div class="progress-fill-inline" id="importProgressFill" style="width: 0%;"></div>
                            </div>
                            <div class="progress-text-inline" id="importProgressText">جاهز للاستخدام</div>
                            <div class="progress-percentage" id="importProgressPercentage">0%</div>
                        </div>
                        
                        <div class="status-message-inline" id="importStatusMessage" style="display: none;">
                            جاري المعالجة...
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="import-stats">
                        <h3>📈 إحصائيات البيانات</h3>
                        <div class="stats-grid-mini">
                            <div class="stat-mini">
                                <div class="stat-value-mini" id="importStudentsCount">0</div>
                                <div class="stat-label-mini">الطلاب</div>
                            </div>
                            <div class="stat-mini">
                                <div class="stat-value-mini" id="importTeachersCount">0</div>
                                <div class="stat-label-mini">الأساتذة</div>
                            </div>
                            <div class="stat-mini">
                                <div class="stat-value-mini" id="importSectionsCount">0</div>
                                <div class="stat-label-mini">الأقسام</div>
                            </div>
                            <div class="stat-mini">
                                <div class="stat-value-mini" id="importCodesCount">0</div>
                                <div class="stat-label-mini">الرموز</div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="control-buttons">
                        <button class="btn" onclick="refreshImportData()">🔄 تحديث البيانات</button>
                        <button class="btn" onclick="showImportHelp()">❓ المساعدة</button>
                        <button class="btn btn-secondary" onclick="navigateToPage('home')">🏠 العودة للرئيسية</button>
                    </div>
                </div>
            `;
        }

        // تهيئة وظائف الاستيراد
        function initializeImportFunctions() {
            // تحديث الإحصائيات في صفحة الاستيراد
            updateImportStatistics();
            
            console.log('✅ تم تهيئة وظائف الاستيراد');
        }

        // تحديث إحصائيات صفحة الاستيراد
        function updateImportStatistics(statsJson) {
            try {
                if (statsJson) {
                    // إذا تم تمرير البيانات كمعامل
                    let stats;
                    if (typeof statsJson === 'string') {
                        stats = JSON.parse(statsJson);
                    } else if (typeof statsJson === 'object') {
                        stats = statsJson;
                    } else {
                        console.error('Invalid statsJson type:', typeof statsJson);
                        return;
                    }
                    displayImportStatistics(stats);
                } else if (importEngine) {
                    // إذا لم يتم تمرير البيانات، احصل عليها من محرك الاستيراد
                    importEngine.getDatabaseStatistics(function(result) {
                        try {
                            let stats;
                            if (typeof result === 'string') {
                                stats = JSON.parse(result);
                            } else if (typeof result === 'object') {
                                stats = result;
                            } else {
                                console.error('Invalid result type:', typeof result);
                                return;
                            }
                            displayImportStatistics(stats);
                        } catch (error) {
                            console.error('خطأ في تحليل بيانات الاستيراد:', error);
                        }
                    });
                } else if (systemEngine) {
                    // إذا لم يكن محرك الاستيراد متوفراً، استخدم النظام الرئيسي
                    systemEngine.getDatabaseStatistics(function(result) {
                        try {
                            let stats;
                            if (typeof result === 'string') {
                                stats = JSON.parse(result);
                            } else if (typeof result === 'object') {
                                stats = result;
                            } else {
                                console.error('Invalid systemEngine result type:', typeof result);
                                return;
                            }
                            displayImportStatistics(stats);
                        } catch (error) {
                            console.error('خطأ في تحليل بيانات النظام:', error);
                        }
                    });
                } else {
                    console.error('لا يمكن الحصول على الإحصائيات');
                    return;
                }
            } catch (error) {
                console.error('خطأ في تحديث إحصائيات الاستيراد:', error);
            }
        }
        
        // عرض إحصائيات الاستيراد
        function displayImportStatistics(stats) {
            try {
                const studentsElement = document.getElementById('importStudentsCount');
                const teachersElement = document.getElementById('importTeachersCount');
                const sectionsElement = document.getElementById('importSectionsCount');
                const codesElement = document.getElementById('importCodesCount');
                const levelsElement = document.getElementById('importLevelsCount');
                
                if (studentsElement) studentsElement.textContent = stats.students_count || 0;
                if (teachersElement) teachersElement.textContent = stats.teachers_count || 0;
                if (sectionsElement) sectionsElement.textContent = stats.sections_count || 0;
                if (codesElement) codesElement.textContent = stats.secret_codes_count || 0;
                if (levelsElement) levelsElement.textContent = stats.levels_count || 0;
                
            } catch (error) {
                console.error('خطأ في عرض إحصائيات الاستيراد:', error);
            }
        }

        // وظائف الاستيراد
        function importMasarData() {
            if (importEngine) {
                updateImportProgress(10, 'بدء استيراد بيانات منظومة مسار...');
                importEngine.selectMasarFile();
            } else {
                alert('❌ محرك الاستيراد غير متوفر');
            }
        }

        function importSecretCodes() {
            if (importEngine) {
                updateImportProgress(10, 'بدء استيراد الرموز السرية...');
                importEngine.selectSecretCodesFiles();
            } else {
                alert('❌ محرك الاستيراد غير متوفر');
            }
        }

        function importTeachers() {
            if (importEngine) {
                updateImportProgress(10, 'بدء استيراد بيانات الأساتذة...');
                importEngine.selectTeachersFile();
            } else {
                alert('❌ محرك الاستيراد غير متوفر');
            }
        }

        function importArrivals() {
            if (importEngine) {
                updateImportProgress(10, 'بدء استيراد بيانات الوافدين والمغادرين...');
                importEngine.importArrivalsData();
            } else {
                alert('❌ محرك الاستيراد غير متوفر');
            }
        }

        function refreshImportData() {
            if (importEngine) {
                updateImportProgress(50, 'جاري تحديث البيانات...');
                importEngine.refreshData();
                setTimeout(() => {
                    updateImportStatistics();
                    updateImportProgress(100, 'تم التحديث بنجاح');
                    setTimeout(() => {
                        resetImportProgress();
                    }, 2000);
                }, 1000);
            } else {
                alert('❌ محرك الاستيراد غير متوفر');
            }
        }

        function showImportHelp() {
            if (importEngine) {
                importEngine.showHelpGuide();
                alert('📖 تم عرض دليل المساعدة في وحدة التحكم');
            } else {
                alert('❌ محرك الاستيراد غير متوفر');
            }
        }

        // تحديث شريط التقدم في صفحة الاستيراد
        function updateImportProgress(percentage, message) {
            const progressFill = document.getElementById('importProgressFill');
            const progressText = document.getElementById('importProgressText');
            const progressPercentage = document.getElementById('importProgressPercentage');
            const statusMessage = document.getElementById('importStatusMessage');

            if (progressFill) progressFill.style.width = percentage + '%';
            if (progressText) progressText.textContent = message;
            if (progressPercentage) progressPercentage.textContent = percentage + '%';
            
            if (statusMessage) {
                if (percentage > 0 && percentage < 100) {
                    statusMessage.style.display = 'block';
                    statusMessage.textContent = message;
                } else if (percentage >= 100) {
                    statusMessage.style.display = 'block';
                    statusMessage.textContent = '✅ ' + message;
                }
            }
        }

        // إعادة تعيين شريط التقدم
        function resetImportProgress() {
            updateImportProgress(0, 'جاهز للاستخدام');
            const statusMessage = document.getElementById('importStatusMessage');
            if (statusMessage) {
                statusMessage.style.display = 'none';
            }
        }

        // إغلاق نظام الاستيراد
        function closeImportSystem() {
            if (systemEngine) {
                systemEngine.closeImportSystem();
            }
        }

        // تحميل نظام الإعدادات
        function loadSettingsSystem() {
            if (systemEngine) {
                systemEngine.openSettingsSystem();
            } else {
                alert('❌ النظام غير جاهز بعد');
            }
        }

        // دوال الإعدادات
        function deleteAllDataWithPassword() {
            if (!settingsEngine) {
                alert('❌ محرك الإعدادات غير متوفر');
                return;
            }
            
            const password = prompt('🔐 الرجاء إدخال رمز الحذف للمتابعة:');
            if (password === null) return; // المستخدم ألغى العملية
            
            updateSettingsProgress(10, 'جاري التحقق من كلمة المرور...');
            
            settingsEngine.delete_all_data(password, function(result) {
                try {
                    let response;
                    if (typeof result === 'string') {
                        response = JSON.parse(result);
                    } else if (typeof result === 'object') {
                        response = result;
                    } else {
                        console.error('Invalid delete result type:', typeof result);
                        alert('❌ خطأ في تحليل النتيجة');
                        return;
                    }
                    
                    if (response.success) {
                        updateSettingsProgress(100, 'تم حذف البيانات بنجاح!');
                        alert('✅ ' + response.message);
                        loadDatabaseInfo(); // تحديث معلومات قاعدة البيانات
                    } else {
                        updateSettingsProgress(0, 'فشل في حذف البيانات');
                        alert('❌ ' + response.message);
                    }
                } catch (error) {
                    console.error('خطأ في تحليل رد الحذف:', error);
                    alert('❌ خطأ في العملية');
                }
            });
        }

        function resetSchoolYearWithPassword() {
            if (!settingsEngine) {
                alert('❌ محرك الإعدادات غير متوفر');
                return;
            }
            
            const password = prompt('🔐 الرجاء إدخال رمز التأكيد للمتابعة:');
            if (password === null) return; // المستخدم ألغى العملية
            
            updateSettingsProgress(10, 'جاري التحقق من كلمة المرور...');
            
            settingsEngine.reset_school_year(password, function(result) {
                try {
                    const response = JSON.parse(result);
                    if (response.success) {
                        updateSettingsProgress(100, 'تم تهيئة السنة الدراسية بنجاح!');
                        alert('✅ ' + response.message);
                        loadDatabaseInfo(); // تحديث معلومات قاعدة البيانات
                    } else {
                        updateSettingsProgress(0, 'فشل في تهيئة السنة الدراسية');
                        alert('❌ ' + response.message);
                    }
                } catch (error) {
                    console.error('خطأ في تحليل رد التهيئة:', error);
                    alert('❌ خطأ في العملية');
                }
            });
        }

        function backupDatabase() {
            if (!settingsEngine) {
                alert('❌ محرك الإعدادات غير متوفر');
                return;
            }
            
            updateSettingsProgress(10, 'جاري إنشاء النسخة الاحتياطية...');
            
            settingsEngine.backup_database(function(result) {
                try {
                    const response = JSON.parse(result);
                    if (response.success) {
                        updateSettingsProgress(100, 'تم إنشاء النسخة الاحتياطية بنجاح!');
                        alert('✅ ' + response.message + '\\n\\nاسم الملف: ' + response.filename + '\\nالحجم: ' + response.size);
                    } else {
                        updateSettingsProgress(0, 'فشل في إنشاء النسخة الاحتياطية');
                        alert('❌ ' + response.message);
                    }
                } catch (error) {
                    console.error('خطأ في تحليل رد النسخ الاحتياطي:', error);
                    alert('❌ خطأ في العملية');
                }
            });
        }

        function insertWeeklyAbsence() {
            if (!settingsEngine) {
                alert('❌ محرك الإعدادات غير متوفر');
                return;
            }
            
            updateSettingsProgress(10, 'جاري إدراج الغياب الأسبوعي...');
            
            settingsEngine.insert_weekly_absence(function(result) {
                try {
                    const response = JSON.parse(result);
                    if (response.success) {
                        updateSettingsProgress(100, 'تم إدراج الغياب الأسبوعي بنجاح!');
                        alert('✅ ' + response.message);
                    } else {
                        updateSettingsProgress(0, 'فشل في إدراج الغياب الأسبوعي');
                        alert('❌ ' + response.message);
                    }
                } catch (error) {
                    console.error('خطأ في تحليل رد الإدراج:', error);
                    alert('❌ خطأ في العملية');
                }
            });
        }

        function showLibrariesStatus() {
            if (!settingsEngine) {
                alert('❌ محرك الإعدادات غير متوفر');
                return;
            }
            
            settingsEngine.get_libraries_status(function(result) {
                try {
                    const response = JSON.parse(result);
                    if (response.error) {
                        alert('❌ خطأ: ' + response.error);
                        return;
                    }
                    
                    let message = '📚 حالة المكتبات:\\n\\n';
                    response.libraries.forEach(lib => {
                        const status = lib.status ? '✅' : '❌';
                        message += `${status} ${lib.name}: ${lib.description}\\n`;
                    });
                    
                    message += '\\n🖥️ معلومات النظام:\\n';
                    message += `إصدار Python: ${response.system.python_version}\\n`;
                    message += `نظام التشغيل: ${response.system.platform}\\n`;
                    
                    message += '\\n💡 نصائح التثبيت:\\n';
                    response.install_tips.forEach(tip => {
                        message += `• ${tip}\\n`;
                    });
                    
                    alert(message);
                } catch (error) {
                    console.error('خطأ في تحليل حالة المكتبات:', error);
                    alert('❌ خطأ في العملية');
                }
            });
        }

        function updateSettingsProgress(percentage, statusText) {
            const progressContainer = document.getElementById('settingsProgressContainer');
            const progressBar = document.getElementById('settingsProgressBar');
            const progressText = document.getElementById('settingsProgressText');
            
            if (progressContainer && progressBar && progressText) {
                progressContainer.style.display = percentage > 0 && percentage < 100 ? 'block' : 'none';
                progressBar.style.width = percentage + '%';
                progressText.textContent = statusText;
            }
        }

        function loadDatabaseInfo() {
            if (!settingsEngine) return;
            
            settingsEngine.get_database_info(function(result) {
                try {
                    const info = JSON.parse(result);
                    if (info.error) {
                        console.error('خطأ في معلومات قاعدة البيانات:', info.error);
                        return;
                    }
                    
                    const dbSize = document.getElementById('dbSize');
                    const dbTables = document.getElementById('dbTables');
                    const dbLastModified = document.getElementById('dbLastModified');
                    
                    if (dbSize) dbSize.textContent = info.size_mb + ' MB';
                    if (dbTables) dbTables.textContent = info.tables_count;
                    if (dbLastModified) dbLastModified.textContent = info.last_modified;
                    
                } catch (error) {
                    console.error('خطأ في تحليل معلومات قاعدة البيانات:', error);
                }
            });
        }

        // عرض مساعدة النظام
        function showSystemHelp() {
            if (systemEngine) {
                systemEngine.showSystemHelp();
                alert('📖 تم عرض المساعدة التفصيلية\\n\\nتحقق من وحدة التحكم لقراءة المساعدة الكاملة.');
            } else {
                alert('❌ النظام غير جاهز بعد');
            }
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌟 تحميل النظام التعليمي الشامل - ملف واحد شامل...');

            // تحديث الوقت
            updateTime();
            setInterval(updateTime, 1000);

            // تهيئة قناة التواصل
            initializeChannel();

            // تحديث الإحصائيات كل 30 ثانية
            setInterval(() => {
                if (systemEngine) {
                    loadDatabaseStatistics();
                }
            }, 30000);

            // تحديد الصفحة الافتراضية
            navigateToPage('home');

            // إضافة مستمع لمفتاح Escape لإغلاق النوافذ المدمجة
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    closeImportSystem();
                }
            });
        });

        // =============== دوال صفحة المؤسسة ===============
        
        // تحميل بيانات المؤسسة
        function loadInstitutionData() {
            if (institutionEngine) {
                try {
                    const dataResult = institutionEngine.getInstitutionData();
                    let data;
                    if (typeof dataResult === 'string') {
                        data = JSON.parse(dataResult);
                    } else if (typeof dataResult === 'object') {
                        data = dataResult;
                    } else {
                        console.error('Invalid institution data result type:', typeof dataResult);
                        return;
                    }

                    if (data.error) {
                        console.error('Error loading institution data:', data.error);
                        return;
                    }

                    // ملء النموذج بالبيانات
                    document.getElementById('academy').value = data.الأكاديمية || '';
                    document.getElementById('directorate').value = data.المديرية || '';
                    document.getElementById('commune').value = data.الجماعة || '';
                    document.getElementById('school').value = data.المؤسسة || '';
                    document.getElementById('town').value = data.البلدة || '';
                    document.getElementById('registrationNumber').value = data.رقم_التسجيل || '';

                    // القوائم المنسدلة
                    const academicYear = document.getElementById('academicYear');
                    if (academicYear && data.السنة_الدراسية) {
                        academicYear.value = data.السنة_الدراسية;
                    }

                    const director = document.getElementById('director');
                    if (director && data.المدير) {
                        director.value = data.المدير;
                    }

                    const guardian = document.getElementById('guardian');
                    if (guardian && data.الحارس_العام) {
                        guardian.value = data.الحارس_العام;
                    }

                    const level = document.getElementById('level');
                    if (level && data.السلك) {
                        level.value = data.السلك;
                    }

                    const guardNumber = document.getElementById('guardNumber');
                    if (guardNumber && data.رقم_الحراسة) {
                        guardNumber.value = data.رقم_الحراسة;
                    }

                    const semester = document.getElementById('semester');
                    if (semester && data.الأسدس) {
                        semester.value = data.الأسدس;
                    }

                    console.log('✅ تم تحميل بيانات المؤسسة بنجاح');
                } catch (error) {
                    console.error('خطأ في تحميل بيانات المؤسسة:', error);
                }
            }
        }

        // حفظ بيانات المؤسسة
        function saveInstitutionData() {
            if (!institutionEngine) {
                alert('❌ محرك المؤسسة غير متوفر');
                return;
            }

            updateInstitutionProgress(10, 'جاري جمع البيانات...');

            // جمع البيانات من النموذج
            const formData = {
                الأكاديمية: document.getElementById('academy').value,
                المديرية: document.getElementById('directorate').value,
                الجماعة: document.getElementById('commune').value,
                المؤسسة: document.getElementById('school').value,
                السنة_الدراسية: document.getElementById('academicYear').value,
                البلدة: document.getElementById('town').value,
                المدير: document.getElementById('director').value,
                الحارس_العام: document.getElementById('guardian').value,
                السلك: document.getElementById('level').value,
                رقم_الحراسة: document.getElementById('guardNumber').value,
                رقم_التسجيل: document.getElementById('registrationNumber').value,
                الأسدس: document.getElementById('semester').value
            };

            updateInstitutionProgress(30, 'جاري حفظ البيانات...');

            try {
                const result = institutionEngine.saveInstitutionData(JSON.stringify(formData));
                let response;
                if (typeof result === 'string') {
                    response = JSON.parse(result);
                } else if (typeof result === 'object') {
                    response = result;
                } else {
                    throw new Error('Invalid response type');
                }

                if (response.success) {
                    updateInstitutionProgress(100, 'تم الحفظ بنجاح!');
                    alert('✅ ' + response.message);
                    // تحديث رمز المؤسسة
                    updateInstitutionCode();
                } else {
                    updateInstitutionProgress(0, 'فشل في الحفظ');
                    alert('❌ ' + response.message);
                }
            } catch (error) {
                updateInstitutionProgress(0, 'فشل في الحفظ');
                console.error('خطأ في حفظ بيانات المؤسسة:', error);
                alert('❌ خطأ في حفظ البيانات');
            }

            setTimeout(() => {
                resetInstitutionProgress();
            }, 3000);
        }

        // اختيار شعار المؤسسة
        function selectInstitutionLogo() {
            if (institutionEngine) {
                updateInstitutionProgress(10, 'جاري اختيار الشعار...');
                institutionEngine.selectLogo();
                
                // تحديث عرض الشعار بعد الاختيار
                setTimeout(() => {
                    loadInstitutionLogo();
                    updateInstitutionProgress(100, 'تم تحميل الشعار');
                    setTimeout(resetInstitutionProgress, 2000);
                }, 1000);
            } else {
                alert('❌ محرك المؤسسة غير متوفر');
            }
        }

        // تحميل الشعار
        function loadInstitutionLogo() {
            if (institutionEngine) {
                try {
                    const logoResult = institutionEngine.getLogoPath();
                    let logoInfo;
                    if (typeof logoResult === 'string') {
                        logoInfo = JSON.parse(logoResult);
                    } else if (typeof logoResult === 'object') {
                        logoInfo = logoResult;
                    } else {
                        return;
                    }

                    const logoDisplay = document.getElementById('institutionLogo');
                    if (logoDisplay) {
                        if (logoInfo.exists && logoInfo.path) {
                            logoDisplay.style.backgroundImage = `url('file:///${logoInfo.path}')`;
                            logoDisplay.innerHTML = '';
                        } else {
                            logoDisplay.style.backgroundImage = 'none';
                            logoDisplay.innerHTML = '<span>لا يوجد شعار</span>';
                        }
                    }
                } catch (error) {
                    console.error('خطأ في تحميل الشعار:', error);
                }
            }
        }

        // عرض رمز المؤسسة
        function showInstitutionCode() {
            if (institutionEngine) {
                try {
                    const codeResult = institutionEngine.getInstitutionCode();
                    let codeInfo;
                    if (typeof codeResult === 'string') {
                        codeInfo = JSON.parse(codeResult);
                    } else if (typeof codeResult === 'object') {
                        codeInfo = codeResult;
                    } else {
                        alert('❌ خطأ في الحصول على رمز المؤسسة');
                        return;
                    }

                    if (codeInfo.error) {
                        alert('❌ ' + codeInfo.error);
                    } else {
                        alert('🏷️ رمز المؤسسة: ' + codeInfo.code);
                    }
                } catch (error) {
                    console.error('خطأ في عرض رمز المؤسسة:', error);
                    alert('❌ خطأ في عرض رمز المؤسسة');
                }
            } else {
                alert('❌ محرك المؤسسة غير متوفر');
            }
        }

        // تحديث رمز المؤسسة في الواجهة
        function updateInstitutionCode() {
            if (institutionEngine) {
                try {
                    const codeResult = institutionEngine.getInstitutionCode();
                    let codeInfo;
                    if (typeof codeResult === 'string') {
                        codeInfo = JSON.parse(codeResult);
                    } else if (typeof codeResult === 'object') {
                        codeInfo = codeResult;
                    } else {
                        return;
                    }

                    // يمكن إضافة عنصر لعرض رمز المؤسسة في الواجهة إذا لزم الأمر
                    console.log('رمز المؤسسة:', codeInfo.code);
                } catch (error) {
                    console.error('خطأ في تحديث رمز المؤسسة:', error);
                }
            }
        }

        // عرض معلومات المؤسسة
        function showInstitutionInfo() {
            if (institutionEngine) {
                try {
                    const infoResult = institutionEngine.getInstitutionInfo();
                    let infoData;
                    if (typeof infoResult === 'string') {
                        infoData = JSON.parse(infoResult);
                    } else if (typeof infoResult === 'object') {
                        infoData = infoResult;
                    } else {
                        alert('❌ خطأ في الحصول على معلومات المؤسسة');
                        return;
                    }

                    if (infoData.success && infoData.info) {
                        const info = infoData.info;
                        const message = `
📋 معلومات المؤسسة:

🏛️ الأكاديمية: ${info.academy}
🏢 المديرية: ${info.directorate}
🏫 المؤسسة: ${info.school}
🏷️ رمز المؤسسة: ${info.institution_code}
📄 رقم التسجيل: ${info.registration_code}
                        `;
                        alert(message);
                    } else {
                        alert('❌ ' + infoData.message);
                    }
                } catch (error) {
                    console.error('خطأ في عرض معلومات المؤسسة:', error);
                    alert('❌ خطأ في عرض معلومات المؤسسة');
                }
            } else {
                alert('❌ محرك المؤسسة غير متوفر');
            }
        }

        // تحديث بيانات المؤسسة
        function refreshInstitutionData() {
            if (institutionEngine) {
                updateInstitutionProgress(50, 'جاري تحديث البيانات...');
                institutionEngine.refreshData();
                
                setTimeout(() => {
                    loadInstitutionData();
                    loadInstitutionLogo();
                    updateInstitutionProgress(100, 'تم التحديث بنجاح');
                    setTimeout(resetInstitutionProgress, 2000);
                }, 1000);
            } else {
                alert('❌ محرك المؤسسة غير متوفر');
            }
        }

        // تحديث شريط التقدم للمؤسسة
        function updateInstitutionProgress(percentage, message) {
            const progressFill = document.getElementById('institutionProgressBar');
            const progressText = document.getElementById('institutionProgressText');
            const progressContainer = document.getElementById('institutionProgressContainer');

            if (progressContainer) {
                if (percentage > 0) {
                    progressContainer.style.display = 'block';
                } else {
                    progressContainer.style.display = 'none';
                }
            }

            if (progressFill) progressFill.style.width = percentage + '%';
            if (progressText) progressText.textContent = message;
        }

        // إعادة تعيين شريط التقدم للمؤسسة
        function resetInstitutionProgress() {
            updateInstitutionProgress(0, 'جاهز للاستخدام');
        }

        // تحميل السنوات الدراسية في قائمة المؤسسة
        function loadInstitutionAcademicYears() {
            if (institutionEngine) {
                try {
                    const yearsResult = institutionEngine.getAcademicYears();
                    let yearsData;
                    if (typeof yearsResult === 'string') {
                        yearsData = JSON.parse(yearsResult);
                    } else if (typeof yearsResult === 'object') {
                        yearsData = yearsResult;
                    } else {
                        return;
                    }

                    const academicYearSelect = document.getElementById('academicYear');
                    if (academicYearSelect && yearsData.years) {
                        academicYearSelect.innerHTML = '';
                        yearsData.years.forEach(year => {
                            const option = document.createElement('option');
                            option.value = year;
                            option.textContent = year;
                            academicYearSelect.appendChild(option);
                        });
                    }
                } catch (error) {
                    console.error('خطأ في تحميل السنوات الدراسية:', error);
                }
            }
        }

        // تهيئة صفحة المؤسسة
        function initializeInstitutionPage() {
            console.log('🏢 تهيئة صفحة المؤسسة...');
            
            // تحميل البيانات
            loadInstitutionData();
            loadInstitutionLogo();
            loadInstitutionAcademicYears();
            
            console.log('✅ تم تهيئة صفحة المؤسسة');
        }        console.log('🚀 تم تحميل النظام الشامل بنجاح!');
    </script>
</body>
</html>
        """

def main():
    """تشغيل النظام الرئيسي - ملف واحد شامل"""
    app = QApplication(sys.argv)

    # إعداد التطبيق
    app.setApplicationName("النظام التعليمي الشامل - ملف واحد شامل")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Modern Education Systems")

    # إنشاء النافذة الرئيسية
    window = ModernSPASystem()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == '__main__':
    print("🌐 بدء تشغيل النظام التعليمي الشامل - ملف واحد شامل...")
    print("=" * 60)
    print("📋 الميزات:")
    print("   🔹 ملف واحد فقط - لا حاجة لملفات أخرى")
    print("   🔹 تنقل سلس مثل صفحات الإنترنت")
    print("   🔹 شريط تنقل علوي ثابت")
    print("   🔹 صفحات محملة ديناميكياً")
    print("   🔹 بدون تموجات أو تأثيرات مزعجة")
    print("   🔹 أداء محسن ومستقر")
    print("=" * 60)
    print("🎯 كيفية الاستخدام:")
    print("   • استخدم شريط التنقل العلوي للانتقال بين الصفحات")
    print("   • انقر على بطاقات الوحدات في الصفحة الرئيسية")
    print("   • تابع الإحصائيات المحدثة في الصفحة الرئيسية")
    print("=" * 60)
    print("🚀 جاري تشغيل النظام...")

    try:
        main()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("💡 تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5 PyQtWebEngine")