#!/usr/bin/env python3
"""
اختبار بسيط لمحرك بيانات المؤسسة
"""

try:
    print("🔧 محاولة استيراد InstitutionEngine...")
    from sub2_window_html import InstitutionEngine
    print("✅ تم الاستيراد بنجاح")
    
    print("🔧 محاولة إنشاء المحرك...")
    engine = InstitutionEngine()
    print("✅ تم إنشاء المحرك بنجاح")
    
    print("🔧 فحص الدوال المتاحة...")
    methods = [method for method in dir(engine) if not method.startswith('_')]
    print(f"✅ الدوال المتاحة: {methods}")
    
    print("🔧 التحقق من وجود get_complete_html...")
    if hasattr(engine, 'get_complete_html'):
        print("✅ دالة get_complete_html موجودة")
        
        print("🔧 محاولة استدعاء الدالة...")
        html = engine.get_complete_html()
        
        if html and len(html) > 100:
            print(f"✅ تم تحميل HTML بنجاح ({len(html)} حرف)")
            print(f"🔍 أول 200 حرف: {html[:200]}...")
        else:
            print("❌ HTML فارغ أو صغير جداً")
            
    else:
        print("❌ دالة get_complete_html غير موجودة")
        
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    print(f"تفاصيل: {traceback.format_exc()}")
