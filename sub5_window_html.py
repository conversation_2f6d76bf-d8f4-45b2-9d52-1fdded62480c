"""
نافذة الإحصائيات العامة - Python + HTML
تم إعادة تصميم النافذة لتستخدم منهجية Python + HTML الحديثة

الميزات:
- واجهة HTML جميلة ومتجاوبة مع بطاقات أنيقة
- تكامل كامل مع قاعدة البيانات
- إحصائيات تفاعلية للطلاب والأقسام والمستويات
- تقارير قابلة للطباعة
- تصميم عصري ومرن
"""

import sys
import os
import json
import sqlite3
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QMessageBox, QDialog, QCalendarWidget
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, Qt
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtWidgets import QPushButton

# استيراد فئة تقرير الإحصائيات
try:
    from print0 import StatisticsReport
    STATISTICS_REPORT_AVAILABLE = True
except ImportError:
    STATISTICS_REPORT_AVAILABLE = False
    print("تعذر استيراد وحدة StatisticsReport - ستعمل النافذة بدون ميزة الطباعة")

class StatisticsEngine(QObject):
    """محرك إدارة الإحصائيات"""

    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    dataUpdated = pyqtSignal(str)  # data JSON

    def __init__(self):
        super().__init__()
        self.db_path = "data.db"
        self.stats_report = None
        if STATISTICS_REPORT_AVAILABLE:
            self.stats_report = StatisticsReport(self.db_path)

    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.logUpdated.emit(message, status, timestamp)

    @pyqtSlot(result=str)
    def getStatisticsData(self):
        """الحصول على جميع الإحصائيات"""
        try:
            # الحصول على السنة الدراسية
            academic_year = self.get_academic_year()
            
            # الحصول على الإحصائيات
            stats_data = self.get_statistics(academic_year)
            
            # إضافة السنة الدراسية للنتائج
            stats_data["academic_year"] = academic_year
            
            # الحصول على معلومات المؤسسة
            school_info = self.get_school_info()
            stats_data["school_info"] = school_info
            
            return json.dumps(stats_data, ensure_ascii=False)
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في جلب الإحصائيات: {str(e)}", "error")
            return json.dumps({}, ensure_ascii=False)

    def get_academic_year(self):
        """استخراج السنة الدراسية من قاعدة البيانات"""
        try:
            if self.stats_report:
                return self.stats_report.get_academic_year()
            
            # طريقة احتياطية
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            conn.close()
            
            return result[0] if result and result[0] else ""
            
        except Exception as e:
            self.emit_log(f"خطأ في استخراج السنة الدراسية: {str(e)}", "error")
            return ""

    def get_statistics(self, academic_year=""):
        """استخراج الإحصائيات من قاعدة البيانات"""
        try:
            if self.stats_report:
                return self.stats_report.get_statistics(academic_year)
            
            # طريقة احتياطية للحصول على الإحصائيات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إحصائيات أساسية
            stats = {
                "students": 0,
                "sections": 0,
                "levels": 0,
                "males": 0,
                "females": 0
            }
            
            # عدد الطلاب الإجمالي
            where_clause = ""
            params = []
            if academic_year:
                where_clause = "WHERE السنة_الدراسية = ?"
                params = [academic_year]
            
            cursor.execute(f"SELECT COUNT(*) FROM البيانات_التلاميذ {where_clause}", params)
            result = cursor.fetchone()
            stats["students"] = result[0] if result else 0
            
            # عدد الذكور والإناث
            cursor.execute(f"SELECT النوع, COUNT(*) FROM البيانات_التلاميذ {where_clause} GROUP BY النوع", params)
            gender_results = cursor.fetchall()
            
            for gender, count in gender_results:
                if gender == "ذكر":
                    stats["males"] = count
                elif gender == "أنثى":
                    stats["females"] = count
            
            # عدد الأقسام المختلفة
            cursor.execute(f"SELECT COUNT(DISTINCT القسم) FROM البيانات_التلاميذ {where_clause}", params)
            result = cursor.fetchone()
            stats["sections"] = result[0] if result else 0
            
            # عدد المستويات المختلفة
            cursor.execute(f"SELECT COUNT(DISTINCT المستوى) FROM البيانات_التلاميذ {where_clause}", params)
            result = cursor.fetchone()
            stats["levels"] = result[0] if result else 0
            
            conn.close()
            return stats
            
        except Exception as e:
            self.emit_log(f"خطأ في حساب الإحصائيات: {str(e)}", "error")
            return {
                "students": 0,
                "sections": 0,
                "levels": 0,
                "males": 0,
                "females": 0
            }

    def get_school_info(self):
        """الحصول على معلومات المؤسسة"""
        try:
            if self.stats_report:
                return self.stats_report.get_school_info()
            
            # طريقة احتياطية
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            conn.close()
            
            if result:
                columns = [description[0] for description in cursor.description]
                return dict(zip(columns, result))
            
            return {}
            
        except Exception as e:
            self.emit_log(f"خطأ في جلب معلومات المؤسسة: {str(e)}", "error")
            return {}

    @pyqtSlot()
    def refreshData(self):
        """تحديث جميع البيانات"""
        try:
            self.emit_log("🔄 جاري تحديث الإحصائيات...", "info")
            self.dataUpdated.emit("refresh")
            self.emit_log("✅ تم تحديث الإحصائيات بنجاح", "success")
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في تحديث الإحصائيات: {str(e)}", "error")

    @pyqtSlot()
    def printReport(self):
        """طباعة التقرير"""
        try:
            if not STATISTICS_REPORT_AVAILABLE:
                self.emit_log("❌ ميزة الطباعة غير متوفرة - تعذر استيراد StatisticsReport", "error")
                return
            
            self.emit_log("🖨️ جاري إعداد التقرير للطباعة...", "info")
            
            # هنا يمكن إضافة منطق الطباعة
            # سيتم استدعاء نافذة اختيار التاريخ ثم إنشاء التقرير
            
            self.emit_log("✅ تم إعداد التقرير بنجاح", "success")
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في طباعة التقرير: {str(e)}", "error")


class StatisticsWindow(QMainWindow):
    """نافذة الإحصائيات العامة"""
    
    def __init__(self, parent=None, db_path="data.db"):
        super().__init__(parent)
        self.setWindowTitle("📊 الإحصائيات العامة")
        
        # إزالة أزرار التحكم وإظهار شريط العنوان فقط
        self.setWindowFlags(Qt.Window | Qt.CustomizeWindowHint | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
          # تحديد حجم النافذة
        self.setMinimumSize(1300, 700)
        self.resize(1300, 700)

        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء محرك الإحصائيات
        self.stats_engine = StatisticsEngine()

        # إعداد الواجهة
        self.setup_ui()
        self.setup_web_channel()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء عرض الويب
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)

        # تحميل واجهة HTML
        html_content = self.get_complete_html()
        self.web_view.setHtml(html_content)

    def setup_web_channel(self):
        """إعداد قناة التواصل"""
        self.channel = QWebChannel()
        self.channel.registerObject("statsEngine", self.stats_engine)
        self.web_view.page().setWebChannel(self.channel)
        self.web_view.loadFinished.connect(self.on_page_loaded)

    def on_page_loaded(self):
        """استدعاء عند انتهاء تحميل الصفحة"""
        self.channel.registerObject("statsEngine", self.stats_engine)

    def get_complete_html(self):
        """HTML كامل مع CSS و JavaScript"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <title>الإحصائيات العامة</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
            overflow-x: hidden;
        }

        /* إخفاء أشرطة التمرير من العناصر العامة */
        body::-webkit-scrollbar {
            width: 8px;
        }
        
        body::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }
        
        body::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 10px;
        }        .container {
            max-width: 1350px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-family: 'Calibri', sans-serif;
            font-size: 24pt;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .header p {
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            color: #666;
        }

        .stats-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-family: 'Calibri', sans-serif;
            font-size: 20pt;
            font-weight: bold;
            color: white;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }        .stats-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
            min-height: 180px;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color, #667eea);
            border-radius: 20px 20px 0 0;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        }        .stats-icon {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: var(--card-color, #667eea);
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .stats-value {
            font-family: 'Calibri', sans-serif;
            font-size: 28pt;
            font-weight: bold;
            color: var(--card-color, #667eea);
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            direction: ltr;
        }

        .stats-label {
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            font-weight: bold;
            color: #555;
            margin-bottom: 5px;
        }

        .stats-description {
            font-family: 'Calibri', sans-serif;
            font-size: 10pt;
            color: #777;
            font-style: italic;
        }

        /* ألوان البطاقات */
        .card-students { --card-color: #2196f3; }
        .card-sections { --card-color: #4caf50; }
        .card-levels { --card-color: #ff9800; }
        .card-average { --card-color: #9c27b0; }
        .card-males { --card-color: #3f51b5; }
        .card-females { --card-color: #e91e63; }
        .card-total { --card-color: #009688; }
        .card-percentage { --card-color: #ffc107; }

        /* أزرار التحكم */
        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn {
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            font-weight: bold;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            min-width: 140px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(86, 171, 47, 0.4);
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
        }

        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }

        /* رسائل الحالة */
        .message-box {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            font-family: 'Calibri', sans-serif;
            font-size: 13pt;
            font-weight: bold;
            color: white;
            display: none;
            z-index: 1000;
            box-shadow: 0 5px 20px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            max-width: 300px;
        }

        .message-box.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .message-box.error {
            background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
        }

        .message-box.warning {
            background: linear-gradient(135deg, #f39c12 0%, #f8c471 100%);
        }

        .message-box.info {
            background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
        }

        .message-box.show {
            display: block;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 1200px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
        }
        
        @media (max-width: 800px) {
            .container {
                padding: 10px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 250px;
            }
        }

        /* تأثيرات إضافية */
        .loading-spinner {
            display: none;
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255,255,255,0.3);
            border-top: 5px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- الرأس -->
        <div class="header fade-in">
            <h1>📊 الإحصائيات العامة</h1>
            <p id="academic-year">للسنة الدراسية: <span id="year-display">جاري التحميل...</span></p>
            <p id="school-name">مؤسسة التعليم</p>
        </div>

        <!-- القسم الأول: الإحصائيات العامة -->
        <div class="stats-section fade-in">
            <h2 class="section-title">📈 الإحصائيات العامة</h2>
            <div class="stats-grid" id="general-stats">
                <!-- بطاقة إجمالي التلاميذ -->
                <div class="stats-card card-students">
                    <div class="stats-icon">👥</div>
                    <div class="stats-value" id="total-students">0</div>
                    <div class="stats-label">إجمالي التلاميذ</div>
                    <div class="stats-description">العدد الكلي للطلاب المسجلين</div>
                </div>

                <!-- بطاقة عدد الأقسام -->
                <div class="stats-card card-sections">
                    <div class="stats-icon">🏫</div>
                    <div class="stats-value" id="total-sections">0</div>
                    <div class="stats-label">عدد الأقسام</div>
                    <div class="stats-description">الأقسام المختلفة في المؤسسة</div>
                </div>

                <!-- بطاقة عدد المستويات -->
                <div class="stats-card card-levels">
                    <div class="stats-icon">📚</div>
                    <div class="stats-value" id="total-levels">0</div>
                    <div class="stats-label">عدد المستويات</div>
                    <div class="stats-description">المستويات التعليمية المتاحة</div>
                </div>

                <!-- بطاقة معدل التلاميذ لكل قسم -->
                <div class="stats-card card-average">
                    <div class="stats-icon">📊</div>
                    <div class="stats-value" id="average-per-section">0</div>
                    <div class="stats-label">معدل التلاميذ/قسم</div>
                    <div class="stats-description">متوسط عدد الطلاب في كل قسم</div>
                </div>
            </div>
        </div>

        <!-- القسم الثاني: الإحصائيات حسب النوع -->
        <div class="stats-section fade-in">
            <h2 class="section-title">👫 الإحصائيات حسب النوع</h2>
            <div class="stats-grid" id="gender-stats">
                <!-- بطاقة عدد الذكور -->
                <div class="stats-card card-males">
                    <div class="stats-icon">👦</div>
                    <div class="stats-value" id="total-males">0</div>
                    <div class="stats-label">عدد الذكور</div>
                    <div class="stats-description">الطلاب الذكور</div>
                </div>

                <!-- بطاقة عدد الإناث -->
                <div class="stats-card card-females">
                    <div class="stats-icon">👧</div>
                    <div class="stats-value" id="total-females">0</div>
                    <div class="stats-label">عدد الإناث</div>
                    <div class="stats-description">الطالبات الإناث</div>
                </div>

                <!-- بطاقة الإجمالي -->
                <div class="stats-card card-total">
                    <div class="stats-icon">👥</div>
                    <div class="stats-value" id="gender-total">0</div>
                    <div class="stats-label">الإجمالي</div>
                    <div class="stats-description">المجموع الكلي</div>
                </div>

                <!-- بطاقة نسبة الإناث -->
                <div class="stats-card card-percentage">
                    <div class="stats-icon">📊</div>
                    <div class="stats-value" id="female-percentage">0%</div>
                    <div class="stats-label">نسبة الإناث</div>
                    <div class="stats-description">النسبة المئوية للإناث</div>
                </div>
            </div>
        </div>

        <!-- أزرار التحكم -->
        <div class="controls fade-in">
            <button class="btn btn-primary" onclick="refreshData()">
                🔄 تحديث البيانات
            </button>
            <button class="btn btn-success" onclick="printReport()">
                🖨️ طباعة التقرير
            </button>
            <button class="btn btn-info" onclick="showDetails()">
                📋 التفاصيل
            </button>
        </div>

        <!-- مؤشر التحميل -->
        <div class="loading-spinner" id="loading-spinner"></div>

        <!-- رسائل الحالة -->
        <div class="message-box" id="messageBox"></div>
    </div>

    <script>
        let statsEngine = null;
        let isChannelReady = false;

        // إعداد قناة التواصل مع Python
        function initializeChannel() {
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    statsEngine = channel.objects.statsEngine;
                    isChannelReady = true;

                    console.log('🚀 QWebChannel initialized successfully');

                    // ربط الإشارات
                    if (statsEngine) {
                        statsEngine.logUpdated.connect(handleLogUpdate);
                        statsEngine.dataUpdated.connect(handleDataUpdate);

                        // تحميل البيانات الأولية
                        loadStatisticsData();

                        console.log('✅ تم تهيئة نظام الإحصائيات بنجاح');
                    }
                });
            } else {
                console.log('⚠️ QWebChannel not available, retrying...');
                setTimeout(initializeChannel, 100);
            }
        }

        // تحميل بيانات الإحصائيات
        function loadStatisticsData() {
            if (statsEngine) {
                showLoading(true);
                statsEngine.getStatisticsData(function(result) {
                    try {
                        let data;
                        if (typeof result === 'string') {
                            data = JSON.parse(result);
                        } else {
                            data = result;
                        }
                        
                        updateStatisticsDisplay(data);
                        showLoading(false);
                    } catch (error) {
                        console.error('خطأ في تحليل بيانات الإحصائيات:', error);
                        showMessage('❌ خطأ في تحميل البيانات', 'error');
                        showLoading(false);
                    }
                });
            }
        }

        // تحديث عرض الإحصائيات
        function updateStatisticsDisplay(data) {
            // تحديث معلومات السنة الدراسية والمؤسسة
            document.getElementById('year-display').textContent = data.academic_year || 'غير محدد';
            
            if (data.school_info && data.school_info.المؤسسة) {
                document.getElementById('school-name').textContent = data.school_info.المؤسسة;
            }

            // تحديث الإحصائيات العامة
            document.getElementById('total-students').textContent = data.students || 0;
            document.getElementById('total-sections').textContent = data.sections || 0;
            document.getElementById('total-levels').textContent = data.levels || 0;
            
            // حساب المعدل
            const avgPerSection = data.sections > 0 ? Math.floor(data.students / data.sections) : 0;
            document.getElementById('average-per-section').textContent = avgPerSection;

            // تحديث إحصائيات النوع
            document.getElementById('total-males').textContent = data.males || 0;
            document.getElementById('total-females').textContent = data.females || 0;
            
            const genderTotal = (data.males || 0) + (data.females || 0);
            document.getElementById('gender-total').textContent = genderTotal;
            
            // حساب نسبة الإناث
            const femalePercentage = genderTotal > 0 ? 
                Math.round((data.females / genderTotal) * 100 * 10) / 10 : 0;
            document.getElementById('female-percentage').textContent = femalePercentage + '%';

            // إضافة تأثير الانيميشن
            animateCards();
        }

        // إضافة تأثيرات الانيميشن للبطاقات
        function animateCards() {
            const cards = document.querySelectorAll('.stats-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        }

        // تحديث البيانات
        function refreshData() {
            if (statsEngine) {
                showMessage('🔄 جاري تحديث البيانات...', 'info');
                statsEngine.refreshData();
                setTimeout(loadStatisticsData, 500);
            } else {
                showMessage('❌ النظام غير جاهز بعد', 'error');
            }
        }

        // طباعة التقرير
        function printReport() {
            if (statsEngine) {
                showMessage('🖨️ جاري إعداد التقرير...', 'info');
                statsEngine.printReport();
            } else {
                showMessage('❌ ميزة الطباعة غير متوفرة', 'error');
            }
        }

        // عرض التفاصيل
        function showDetails() {
            showMessage('📋 عرض التفاصيل قريباً...', 'info');
        }

        // عرض/إخفاء مؤشر التحميل
        function showLoading(show) {
            const spinner = document.getElementById('loading-spinner');
            spinner.style.display = show ? 'block' : 'none';
        }

        // معالجة تحديث السجل
        function handleLogUpdate(message, status, timestamp) {
            showMessage(message, status);
        }

        // معالجة تحديث البيانات
        function handleDataUpdate(action) {
            if (action === 'refresh') {
                loadStatisticsData();
            }
        }

        // عرض الرسائل
        function showMessage(message, type) {
            const messageBox = document.getElementById('messageBox');
            messageBox.textContent = message;
            messageBox.className = `message-box ${type} show`;
            
            setTimeout(() => {
                messageBox.classList.remove('show');
            }, 3000);
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeChannel();
            
            // إضافة تأثيرات إضافية
            setTimeout(() => {
                document.querySelectorAll('.fade-in').forEach((element, index) => {
                    element.style.opacity = '0';
                    element.style.transform = 'translateY(30px)';
                    
                    setTimeout(() => {
                        element.style.transition = 'all 0.6s ease';
                        element.style.opacity = '1';
                        element.style.transform = 'translateY(0)';
                    }, index * 200);
                });
            }, 100);
        });
    </script>
</body>
</html>"""


def main():
    """تشغيل نافذة الإحصائيات العامة"""
    app = QApplication(sys.argv)

    # إعداد التطبيق
    app.setApplicationName("الإحصائيات العامة")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Modern Education Systems")

    # إنشاء النافذة
    window = StatisticsWindow()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())


if __name__ == '__main__':
    print("📊 بدء تشغيل نظام الإحصائيات العامة...")
    print("=" * 60)
    print("📋 الميزات:")
    print("   🔹 واجهة HTML جميلة مع بطاقات أنيقة")
    print("   🔹 إحصائيات تفاعلية شاملة")
    print("   🔹 تكامل كامل مع قاعدة البيانات")
    print("   🔹 تقارير قابلة للطباعة")
    print("   🔹 تصميم عصري ومتجاوب")
    print("=" * 60)
    print("🚀 جاري تشغيل النظام...")

    try:
        main()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("💡 تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5 PyQtWebEngine")
