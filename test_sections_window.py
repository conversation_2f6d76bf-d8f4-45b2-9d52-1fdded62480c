#!/usr/bin/env python3
"""
اختبار بسيط لنافذة البنية التربوية
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_sections_window():
    """اختبار فتح نافذة البنية التربوية"""
    app = QApplication(sys.argv)
    
    try:
        from sub3_window_html import SectionsManagementWindow
        
        print("✅ تم استيراد SectionsManagementWindow بنجاح")
        
        # إنشاء النافذة
        window = SectionsManagementWindow()
        print("✅ تم إنشاء النافذة بنجاح")
        
        # عرض النافذة
        window.show()
        print("✅ تم عرض النافذة بنجاح")
        
        # التحقق من الدوال المتاحة
        if hasattr(window, 'show'):
            print("✅ الدالة show متاحة")
        if hasattr(window, 'move'):
            print("✅ الدالة move متاحة")
        if hasattr(window, 'activateWindow'):
            print("✅ الدالة activateWindow متاحة")
        if hasattr(window, 'raise_'):
            print("✅ الدالة raise_ متاحة")
        
        print("🎯 النافذة جاهزة وتعمل بشكل صحيح!")
        
        # تشغيل التطبيق لمدة قصيرة ثم إغلاقه
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(3000, app.quit)
        
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return 1
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return 1

if __name__ == '__main__':
    print("🔧 اختبار نافذة البنية التربوية...")
    print("=" * 50)
    
    exit_code = test_sections_window()
    
    print("=" * 50)
    if exit_code == 0:
        print("✅ الاختبار نجح!")
    else:
        print("❌ الاختبار فشل!")
    
    sys.exit(exit_code)
