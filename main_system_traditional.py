"""
النظام التعليمي الشامل - الواجهة التقليدية
نافذة رئيسية بسيطة تفتح النوافذ المنفصلة بواجهة PyQt5 تقليدية

الميزات:
- نافذة رئيسية بسيطة وأنيقة
- فتح النوافذ المنفصلة حسب الحاجة
- أداء ممتاز وذاكرة أقل
- سهولة الصيانة والتطوير
- واجهة PyQt5 تقليدية سريعة
"""

import sys
import os
import sqlite3
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QLabel, QFrame, QScrollArea, 
                             QTextEdit, QGroupBox, QGridLayout, QMessageBox,
                             QSizePolicy, QSpacerItem)
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QIcon, QFont, QPalette, QColor, QPixmap


class SystemStatsEngine(QObject):
    """محرك إحصائيات النظام"""
    
    # إشارات
    statsUpdated = pyqtSignal(dict)
    logUpdated = pyqtSignal(str, str)  # message, level
    
    def __init__(self):
        super().__init__()
        self.db_path = "data.db"
        
        # مراجع للنوافذ المنفصلة
        self.import_window = None
        self.settings_window = None
        self.institution_window = None
        self.institution_html_window = None
        self.statistics_window = None
        self.sub551_window = None

    def get_database_statistics(self):
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            if not os.path.exists(self.db_path):
                stats = {
                    "students_count": 0,
                    "teachers_count": 0,
                    "sections_count": 0,
                    "levels_count": 0,
                    "secret_codes_count": 0,
                    "current_year": "غير محدد",
                    "database_size": 0,
                    "tables_count": 0
                }
            else:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                stats = {}
                
                try:
                    # السنة الدراسية الحالية
                    cursor.execute("SELECT DISTINCT السنة_الدراسية FROM اللوائح ORDER BY السنة_الدراسية DESC LIMIT 1")
                    result = cursor.fetchone()
                    stats['current_year'] = result[0] if result and result[0] else "غير محدد"
                    
                    # إحصائيات الطلاب
                    cursor.execute("SELECT COUNT(*) FROM اللوائح")
                    stats['students_count'] = cursor.fetchone()[0]
                    
                    # إحصائيات الأساتذة
                    try:
                        cursor.execute("SELECT COUNT(*) FROM الأساتذة")
                        stats['teachers_count'] = cursor.fetchone()[0]
                    except:
                        stats['teachers_count'] = 0
                    
                    # إحصائيات الأقسام
                    cursor.execute("SELECT COUNT(DISTINCT القسم) FROM اللوائح")
                    stats['sections_count'] = cursor.fetchone()[0]
                    
                    # إحصائيات المستويات
                    cursor.execute("SELECT COUNT(DISTINCT المستوى) FROM اللوائح")
                    stats['levels_count'] = cursor.fetchone()[0]
                    
                    # إحصائيات الرموز السرية
                    try:
                        cursor.execute("SELECT COUNT(*) FROM الرمز_السري")
                        stats['secret_codes_count'] = cursor.fetchone()[0]
                    except:
                        stats['secret_codes_count'] = 0
                    
                    # حجم قاعدة البيانات
                    stats['database_size'] = round(os.path.getsize(self.db_path) / (1024 * 1024), 2)
                    
                    # عدد الجداول
                    cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                    stats['tables_count'] = cursor.fetchone()[0]
                    
                except Exception as e:
                    print(f"خطأ في جمع الإحصائيات: {e}")
                    stats = {
                        "students_count": 0,
                        "teachers_count": 0,
                        "sections_count": 0,
                        "levels_count": 0,
                        "secret_codes_count": 0,
                        "current_year": "غير محدد",
                        "database_size": 0,
                        "tables_count": 0
                    }
                finally:
                    conn.close()
            
            self.statsUpdated.emit(stats)
            return stats
            
        except Exception as e:
            self.logUpdated.emit(f"خطأ في جمع إحصائيات النظام: {str(e)}", "error")
            return {}

    def open_import_window(self):
        """فتح نافذة الاستيراد المنفصلة"""
        self.logUpdated.emit("🔄 فتح نافذة الاستيراد التجريبية...", "info")
        
        try:
            from sub551_window import ModernSub1Window
            
            if self.sub551_window is None:
                self.sub551_window = ModernSub1Window()
            
            self.sub551_window.show()
            self.sub551_window.activateWindow()
            self.sub551_window.raise_()
            self.logUpdated.emit("✅ تم فتح نافذة الاستيراد بنجاح", "success")
            
        except ImportError:
            self.logUpdated.emit("❌ خطأ: ملف sub551_window.py غير موجود", "error")
        except Exception as e:
            self.logUpdated.emit(f"❌ خطأ في فتح نافذة الاستيراد: {str(e)}", "error")

    def open_settings_window(self):
        """فتح نافذة الإعدادات المنفصلة"""
        self.logUpdated.emit("🔄 فتح نافذة الإعدادات...", "info")
        
        try:
            from settings_engine import SettingsEngine
            
            if self.settings_window is None:
                self.settings_window = SettingsEngine()
            
            self.settings_window.show()
            self.settings_window.activateWindow()
            self.settings_window.raise_()
            self.logUpdated.emit("✅ تم فتح نافذة الإعدادات بنجاح", "success")
            
        except ImportError:
            self.logUpdated.emit("❌ خطأ: ملف settings_engine.py غير موجود", "error")
        except Exception as e:
            self.logUpdated.emit(f"❌ خطأ في فتح نافذة الإعدادات: {str(e)}", "error")

    def open_institution_window(self):
        """فتح نافذة بيانات المؤسسة التقليدية"""
        self.logUpdated.emit("🔄 فتح نافذة بيانات المؤسسة التقليدية...", "info")
        
        try:
            from sub2_window import SubWindow
            
            if self.institution_window is None:
                self.institution_window = SubWindow()
            
            self.institution_window.show()
            self.institution_window.activateWindow()
            self.institution_window.raise_()
            self.logUpdated.emit("✅ تم فتح نافذة بيانات المؤسسة التقليدية بنجاح", "success")
            
        except ImportError:
            self.logUpdated.emit("❌ خطأ: ملف sub2_window.py غير موجود", "error")
        except Exception as e:
            self.logUpdated.emit(f"❌ خطأ في فتح نافذة بيانات المؤسسة: {str(e)}", "error")

    def open_institution_html_window(self):
        """فتح نافذة بيانات المؤسسة الحديثة (HTML)"""
        self.logUpdated.emit("🔄 فتح نافذة بيانات المؤسسة الحديثة...", "info")
        
        try:
            from sub2_window_html import InstitutionWindow
            
            if self.institution_html_window is None:
                self.institution_html_window = InstitutionWindow()
            
            self.institution_html_window.show()
            self.institution_html_window.activateWindow()
            self.institution_html_window.raise_()
            self.logUpdated.emit("✅ تم فتح نافذة بيانات المؤسسة الحديثة بنجاح", "success")
            
        except ImportError:
            self.logUpdated.emit("❌ خطأ: ملف sub2_window_html.py غير موجود", "error")
        except Exception as e:
            self.logUpdated.emit(f"❌ خطأ في فتح نافذة بيانات المؤسسة الحديثة: {str(e)}", "error")

    def open_statistics_window(self):
        """فتح نافذة الإحصائيات المنفصلة"""
        self.logUpdated.emit("⚠️ نافذة الإحصائيات قيد التطوير", "warning")


class StatCardWidget(QFrame):
    """بطاقة إحصائية مخصصة"""
    
    def __init__(self, title, value, icon="📊", color="#3498db"):
        super().__init__()
        self.setFrameStyle(QFrame.StyledPanel)
        self.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}, stop:1 {self.darken_color(color)});
                border-radius: 10px;
                border: 2px solid {self.darken_color(color)};
                color: white;
                padding: 10px;
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.lighten_color(color)}, stop:1 {color});
                transform: translateY(-2px);
            }}
        """)
        
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignCenter)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 24px; margin-bottom: 5px;")
        layout.addWidget(icon_label)
        
        # القيمة
        self.value_label = QLabel(str(value))
        self.value_label.setAlignment(Qt.AlignCenter)
        self.value_label.setStyleSheet("font-size: 28px; font-weight: bold; margin-bottom: 5px;")
        layout.addWidget(self.value_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 12px; opacity: 0.9;")
        title_label.setWordWrap(True)
        layout.addWidget(title_label)
        
        self.setLayout(layout)
        self.setFixedHeight(120)
    
    def darken_color(self, color):
        """تغميق اللون"""
        color = QColor(color)
        return color.darker(120).name()
    
    def lighten_color(self, color):
        """تفتيح اللون"""
        color = QColor(color)
        return color.lighter(120).name()
    
    def update_value(self, value):
        """تحديث القيمة"""
        self.value_label.setText(str(value))


class ModuleButtonWidget(QPushButton):
    """زر وحدة مخصص"""
    
    def __init__(self, title, description, icon="🔧", callback=None):
        super().__init__()
        
        self.setMinimumHeight(100)
        self.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 2px solid #dee2e6;
                border-radius: 12px;
                padding: 15px;
                text-align: left;
                font-size: 14px;
                color: #343a40;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #e3f2fd, stop:1 #bbdefb);
                border: 2px solid #2196f3;
                color: #1976d2;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #bbdefb, stop:1 #90caf9);
            }
        """)
        
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignCenter)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 32px; margin-bottom: 8px;")
        layout.addWidget(icon_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 5px;")
        layout.addWidget(title_label)
        
        # الوصف
        desc_label = QLabel(description)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("font-size: 11px; color: #6c757d; line-height: 1.3;")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        widget = QWidget()
        widget.setLayout(layout)
        
        main_layout = QVBoxLayout()
        main_layout.addWidget(widget)
        self.setLayout(main_layout)
        
        if callback:
            self.clicked.connect(callback)


class MainSystemTraditionalWindow(QMainWindow):
    """النافذة الرئيسية للنظام - الواجهة التقليدية"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎓 النظام التعليمي الشامل - الواجهة التقليدية")
        self.setGeometry(100, 100, 1100, 700)
        
        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء محرك الإحصائيات
        self.stats_engine = SystemStatsEngine()
        self.stats_engine.statsUpdated.connect(self.update_statistics)
        self.stats_engine.logUpdated.connect(self.add_log_entry)

        # إعداد الواجهة
        self.setup_ui()
        
        # تحديث الإحصائيات كل 30 ثانية
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.refresh_statistics)
        self.stats_timer.start(30000)  # 30 ثانية
        
        # تحميل الإحصائيات الأولية
        self.refresh_statistics()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # تطبيق نمط عام للنافذة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:0.5 #e9ecef, stop:1 #dee2e6);
            }
            QWidget {
                background: transparent;
            }
        """)
        
        # الرأس
        header_layout = self.create_header()
        main_layout.addLayout(header_layout)
        
        # معلومات النظام
        system_info = self.create_system_info()
        main_layout.addWidget(system_info)
        
        # الإحصائيات
        stats_widget = self.create_statistics_section()
        main_layout.addWidget(stats_widget)
        
        # وحدات النظام
        modules_widget = self.create_modules_section()
        main_layout.addWidget(modules_widget)
        
        # سجل الأنشطة
        log_widget = self.create_log_section()
        main_layout.addWidget(log_widget)
        
        central_widget.setLayout(main_layout)

    def create_header(self):
        """إنشاء قسم الرأس"""
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignCenter)
        
        # العنوان الرئيسي
        title = QLabel("🎓 النظام التعليمي الشامل")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        """)
        layout.addWidget(title)
        
        # العنوان الفرعي
        subtitle = QLabel("الواجهة التقليدية - نسخة 3.0")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            font-size: 16px;
            color: #34495e;
            margin-bottom: 10px;
        """)
        layout.addWidget(subtitle)
        
        return layout

    def create_system_info(self):
        """إنشاء قسم معلومات النظام"""
        group = QGroupBox("💡 الميزات الجديدة")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background: rgba(255, 255, 255, 0.8);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background: white;
            }
        """)
        
        layout = QVBoxLayout()
        
        info_text = QLabel("نافذة رئيسية بسيطة تفتح النوافذ المنفصلة • أداء ممتاز وذاكرة أقل • سهولة الصيانة والتطوير")
        info_text.setWordWrap(True)
        info_text.setAlignment(Qt.AlignCenter)
        info_text.setStyleSheet("""
            font-size: 13px;
            color: #2c3e50;
            padding: 10px;
            line-height: 1.4;
        """)
        layout.addWidget(info_text)
        
        group.setLayout(layout)
        group.setMaximumHeight(80)
        return group

    def create_statistics_section(self):
        """إنشاء قسم الإحصائيات"""
        group = QGroupBox("📊 إحصائيات النظام")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background: rgba(255, 255, 255, 0.8);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background: white;
            }
        """)
        
        layout = QGridLayout()
        layout.setSpacing(15)
        
        # إنشاء بطاقات الإحصائيات
        self.students_card = StatCardWidget("👥 الطلاب", "0", "👥", "#3498db")
        self.teachers_card = StatCardWidget("👨‍🏫 الأساتذة", "0", "👨‍🏫", "#e74c3c")
        self.sections_card = StatCardWidget("🏫 الأقسام", "0", "🏫", "#f39c12")
        self.levels_card = StatCardWidget("📚 المستويات", "0", "📚", "#9b59b6")
        self.year_card = StatCardWidget("📅 السنة الدراسية", "غير محدد", "📅", "#e67e22")
        
        # ترتيب البطاقات في الشبكة
        layout.addWidget(self.students_card, 0, 0)
        layout.addWidget(self.teachers_card, 0, 1)
        layout.addWidget(self.sections_card, 0, 2)
        layout.addWidget(self.levels_card, 1, 0)
        layout.addWidget(self.year_card, 1, 1, 1, 2)  # تمديد عبر عمودين
        
        group.setLayout(layout)
        return group

    def create_modules_section(self):
        """إنشاء قسم وحدات النظام"""
        group = QGroupBox("🔧 وحدات النظام")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background: rgba(255, 255, 255, 0.8);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background: white;
            }
        """)
        
        layout = QGridLayout()
        layout.setSpacing(15)
        
        # إنشاء أزرار الوحدات
        import_btn = ModuleButtonWidget(
            "نظام الاستيراد", 
            "استيراد البيانات من ملفات Excel ومنظومة مسار",
            "📥",
            self.stats_engine.open_import_window
        )
        
        settings_btn = ModuleButtonWidget(
            "الإعدادات",
            "النسخ الاحتياطي والصيانة وإعدادات النظام", 
            "⚙️",
            self.stats_engine.open_settings_window
        )
        
        institution_btn = ModuleButtonWidget(
            "بيانات المؤسسة (تقليدي)",
            "إدارة معلومات المؤسسة والشعار - واجهة تقليدية",
            "🏢",
            self.stats_engine.open_institution_window
        )
        
        institution_html_btn = ModuleButtonWidget(
            "بيانات المؤسسة (حديث)",
            "إدارة معلومات المؤسسة والشعار - واجهة HTML حديثة",
            "🌐",
            self.stats_engine.open_institution_html_window
        )
        
        statistics_btn = ModuleButtonWidget(
            "الإحصائيات",
            "التقارير والإحصائيات الشاملة (قيد التطوير)",
            "📊",
            self.stats_engine.open_statistics_window
        )
        
        about_btn = ModuleButtonWidget(
            "حول النظام",
            "معلومات حول النظام والدعم الفني",
            "ℹ️",
            self.show_about
        )
        
        # ترتيب الأزرار في الشبكة
        layout.addWidget(import_btn, 0, 0)
        layout.addWidget(settings_btn, 0, 1)
        layout.addWidget(institution_btn, 0, 2)
        layout.addWidget(institution_html_btn, 1, 0)
        layout.addWidget(statistics_btn, 1, 1)
        layout.addWidget(about_btn, 1, 2)
        
        group.setLayout(layout)
        return group

    def create_log_section(self):
        """إنشاء قسم سجل الأنشطة"""
        group = QGroupBox("📋 سجل الأنشطة")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background: rgba(255, 255, 255, 0.8);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background: white;
            }
        """)
        
        layout = QVBoxLayout()
        
        # إنشاء مساحة النص للسجل
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background: #2c3e50;
                color: #00ff00;
                font-family: 'Courier New', monospace;
                font-size: 11px;
                border: 1px solid #34495e;
                border-radius: 5px;
                padding: 5px;
            }
        """)
        self.log_text.setReadOnly(True)
        
        # إضافة رسالة ترحيبية
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] 🚀 تم تحميل النظام بنجاح")
        
        layout.addWidget(self.log_text)
        group.setLayout(layout)
        return group

    def update_statistics(self, stats):
        """تحديث عرض الإحصائيات"""
        self.students_card.update_value(stats.get('students_count', 0))
        self.teachers_card.update_value(stats.get('teachers_count', 0))
        self.sections_card.update_value(stats.get('sections_count', 0))
        self.levels_card.update_value(stats.get('levels_count', 0))
        self.year_card.update_value(stats.get('current_year', 'غير محدد'))

    def add_log_entry(self, message, level):
        """إضافة رسالة جديدة للسجل"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # تحديد لون النص حسب مستوى الرسالة
        if level == "error":
            color = "#ff4444"
        elif level == "warning":
            color = "#ffaa00"
        elif level == "success":
            color = "#00ff88"
        else:
            color = "#00ff00"
        
        # إضافة الرسالة مع اللون المناسب
        self.log_text.setTextColor(QColor(color))
        self.log_text.append(f"[{timestamp}] {message}")
        
        # التمرير للأسفل
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )

    def refresh_statistics(self):
        """تحديث الإحصائيات"""
        self.stats_engine.get_database_statistics()

    def show_about(self):
        """عرض معلومات حول النظام"""
        about_text = """
🎓 النظام التعليمي الشامل - الواجهة التقليدية

النسخة: 3.0
التطوير: يونيو 2025
المطور: نظم التعليم الحديثة

الميزات:
• نافذة رئيسية بسيطة وأنيقة
• نوافذ منفصلة لكل وحدة
• أداء ممتاز وذاكرة أقل
• سهولة الصيانة والتطوير
• واجهة PyQt5 تقليدية سريعة

الوحدات المتاحة:
📥 نافذة الاستيراد
⚙️ نافذة الإعدادات  
🏢 نافذة بيانات المؤسسة (تقليدي/حديث)
📊 نافذة الإحصائيات (قيد التطوير)
        """
        
        QMessageBox.information(self, "حول النظام", about_text)

    def closeEvent(self, event):
        """التعامل مع إغلاق النافذة"""
        # إيقاف المؤقت
        if hasattr(self, 'stats_timer'):
            self.stats_timer.stop()
        
        event.accept()


def main():
    """تشغيل النظام الرئيسي"""
    app = QApplication(sys.argv)

    # إعداد التطبيق
    app.setApplicationName("النظام التعليمي الشامل - الواجهة التقليدية")
    app.setApplicationVersion("3.0")
    app.setOrganizationName("Modern Education Systems")

    # إنشاء النافذة الرئيسية
    window = MainSystemTraditionalWindow()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())


if __name__ == '__main__':
    print("🌐 بدء تشغيل النظام التعليمي الشامل - الواجهة التقليدية...")
    print("=" * 70)
    print("📋 الميزات الجديدة:")
    print("   🔹 نافذة رئيسية بسيطة وأنيقة")
    print("   🔹 فتح النوافذ المنفصلة حسب الحاجة")
    print("   🔹 أداء ممتاز وذاكرة أقل")
    print("   🔹 سهولة الصيانة والتطوير")
    print("   🔹 واجهة PyQt5 تقليدية سريعة")
    print("=" * 70)
    print("🎯 الوحدات المتاحة:")
    print("   📥 نافذة الاستيراد")
    print("   ⚙️ نافذة الإعدادات")
    print("   🏢 نافذة بيانات المؤسسة (تقليدي/حديث)")
    print("   📊 نافذة الإحصائيات (قيد التطوير)")
    print("=" * 70)
    print("🚀 جاري تشغيل النظام...")

    try:
        main()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("💡 تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5")
