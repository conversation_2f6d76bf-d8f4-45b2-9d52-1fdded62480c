"""
النظام التعليمي الشامل - النافذة الرئيسية مع دمج نافذة الاستيراد (الإصدار المحدث)
نافذة رئيسية حديثة مع QTabWidget حقيقي ودمج نافذة الاستيراد الفعلية كتبويبة

الميزات:
- QTabWidget حقيقي مع عشرة تبويبات احترافية
- دمج نافذة الاستيراد الفعلية (sub1_window_html.py) كتبويبة حقيقية
- تفتح بكامل الشاشة تلقائياً
- تتكيف مع دقة الشاشة
- واجهة احترافية مع تصميم Material Design
- ربط Python + HTML باستخدام QWebChannel
"""

import sys
import os
import json
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QTabWidget, QHBoxLayout, QLabel, QSplitter, QTextEdit)
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QIcon, QFont

# استيراد محرك نافذة الاستيراد ومحرك بيانات المؤسسة
from sub1_window_html import DataImportEngine
from sub2_window_html import InstitutionEngine

class MainWindowEngine(QObject):
    """محرك النافذة الرئيسية - مسؤول عن التحكم في التبويبات"""

    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    tabChanged = pyqtSignal(str)  # active tab id

    def __init__(self):
        super().__init__()
        self.current_tab = "dashboard"
        self.db_path = "data.db"

    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.logUpdated.emit(message, status, timestamp)
        print(f"📝 [{timestamp}] {message}")

    @pyqtSlot(str)
    def switchTab(self, tab_id):
        """تبديل التبويبة النشطة"""
        self.current_tab = tab_id
        self.emit_log(f"تم التبديل إلى تبويبة: {tab_id}", "info")
        self.tabChanged.emit(tab_id)

    @pyqtSlot(result=str)
    def getCurrentTab(self):
        """الحصول على التبويبة النشطة حالياً"""
        return self.current_tab

    @pyqtSlot(result=str)
    def getSystemInfo(self):
        """الحصول على معلومات النظام"""
        try:
            system_info = {
                "version": "4.2 - QTabWidget مع دمج الاستيراد الحقيقي",
                "release_date": "يونيو 2025",
                "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                "database_exists": os.path.exists(self.db_path),
                "tabs_count": 10,
                "current_tab": self.current_tab,
                "import_integrated": True,
                "tab_widget_type": "QTabWidget"
            }
            return json.dumps(system_info, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"error": str(e)}, ensure_ascii=False)

    @pyqtSlot()
    def showTabInfo(self):
        """عرض معلومات حول التبويبات"""
        info_text = """
🎯 النافذة الرئيسية - QTabWidget مع عشرة تبويبات احترافية

التبويبات المتاحة:
📊 لوحة التحكم - نظرة عامة على النظام
📥 الاستيراد - نافذة الاستيراد الفعلية المدمجة (QWebEngineView)
🏢 بيانات المؤسسة - معلومات المؤسسة التعليمية
👥 إدارة الطلاب - قوائم وبيانات الطلاب
👨‍🏫 إدارة المعلمين - بيانات الأساتذة والمعلمين
📚 المناهج والمقررات - إدارة المواد الدراسية
📊 التقارير والإحصائيات - تقارير شاملة ومفصلة
🖨️ الطباعة - إعدادات وقوالب الطباعة
⚙️ الإعدادات - تكوين النظام العام
ℹ️ المساعدة والدعم - أدلة الاستخدام والمساعدة

✨ الجديد: دمج حقيقي لنافذة الاستيراد داخل QTabWidget
        """
        self.emit_log(info_text, "info")

class MainWindow(QMainWindow):
    """النافذة الرئيسية مع QTabWidget ودمج الاستيراد الحقيقي"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎓 النظام التعليمي الشامل - QTabWidget مع الاستيراد المدمج")
        
        # فتح النافذة في كامل الشاشة
        self.showMaximized()

        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء محرك النافذة
        self.window_engine = MainWindowEngine()
        
        # إنشاء محرك الاستيراد ومحرك بيانات المؤسسة
        try:
            print("🔧 إنشاء محرك الاستيراد...")
            self.import_engine = DataImportEngine()
            print("✅ تم إنشاء محرك الاستيراد بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء محرك الاستيراد: {e}")
            self.import_engine = None
        
        try:
            print("🔧 إنشاء محرك بيانات المؤسسة...")
            self.institution_engine = InstitutionEngine()
            print(f"🟢 نوع محرك المؤسسة: {type(self.institution_engine)}")
            
            # التحقق من وجود الدوال المطلوبة
            if hasattr(self.institution_engine, 'get_complete_html'):
                print("✅ دالة get_complete_html متاحة")
                # اختبار استدعاء الدالة
                test_html = self.institution_engine.get_complete_html()
                print(f"🟢 نتيجة get_complete_html: {type(test_html)} | طول: {len(test_html) if test_html else 0}")
                if test_html and len(test_html) > 100:
                    print(f"✅ تم اختبار get_complete_html بنجاح ({len(test_html)} حرف)")
                else:
                    print("⚠️ get_complete_html ترجع محتوى فارغ أو صغير")
            else:
                print("❌ دالة get_complete_html غير موجودة")
                # طباعة جميع الدوال المتاحة للتشخيص
                available_methods = [method for method in dir(self.institution_engine) if not method.startswith('_')]
                print(f"🟡 الدوال المتاحة في InstitutionEngine: {available_methods}")
                
            print("✅ تم إنشاء محرك بيانات المؤسسة بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء محرك بيانات المؤسسة: {e}")
            import traceback
            print(f"تفاصيل الخطأ: {traceback.format_exc()}")
            self.institution_engine = None

        # إعداد الواجهة
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم باستخدام QTabWidget"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)

        # إنشاء QTabWidget احترافي
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        self.tab_widget.setMovable(False)
        self.tab_widget.setTabsClosable(False)
        
        # تحسين المظهر مع الحفاظ على الحواف المستديرة الجميلة للحاوية
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #c0c4fc;
                background-color: #f8f9ff;
                border-radius: 10px;
                margin: 0px;
                padding: 0px;
            }
            QTabWidget::tab-bar {
                alignment: center;
            }
            QTabBar::tab {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                           stop: 0 #e6e9ff, stop: 1 #d0d4ff);
                border: 2px solid #c0c4fc;
                border-bottom-color: #c0c4fc;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                min-width: 100px;
                min-height: 30px;
                padding: 8px 12px;
                margin-right: 2px;
                font-weight: 600;
                color: #2c3e50;
            }
            QTabBar::tab:selected, QTabBar::tab:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                           stop: 0 #667eea, stop: 1 #764ba2);
                color: white;
                border-color: #667eea;
                border-bottom-color: #667eea;
            }
            QTabBar::tab:selected {
                border-bottom-color: #f8f9ff;
                margin-bottom: -2px;
            }
            /* الحفاظ على التنسيق الجميل للمحتوى */
            QTabWidget > QWidget {
                margin: 0px;
                padding: 0px;
            }
        """)

        layout.addWidget(self.tab_widget)

        # إضافة التبويبات
        self.create_tabs()

        # ربط إشارة تغيير التبويبة
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

    def create_tabs(self):
        """إنشاء التبويبات العشرة"""
        
        # 1. لوحة التحكم
        dashboard_widget = self.create_dashboard_widget()
        self.tab_widget.addTab(dashboard_widget, "📊 لوحة التحكم")

        # 2. تبويبة الاستيراد - QWebEngineView مع نافذة الاستيراد الفعلية
        print("📥 إنشاء تبويبة الاستيراد...")
        self.import_web_view = QWebEngineView()
        
        # تحسين QWebEngineView للدمج المثالي - إضافة مسافة داخلية
        self.import_web_view.setStyleSheet("""
            QWebEngineView {
                border: none;
                margin: 8px;
                padding: 0px;
                background: transparent;
                border-radius: 8px;
            }
        """)
        
        # إعداد قناة التواصل أولاً
        self.import_channel = QWebChannel()
        self.import_channel.registerObject("dataEngine", self.import_engine)
        
        # إنشاء كائن وهمي لإغلاق النافذة (معطل في الدمج)
        class ImportWindowProxy(QObject):
            @pyqtSlot()
            def closeWindow(self):
                print("🚫 تم استدعاء closeWindow - معطلة في الوضع المدمج")
                pass
        
        self.import_window_proxy = ImportWindowProxy()
        self.import_channel.registerObject("importWindow", self.import_window_proxy)
        
        # ربط القناة بصفحة الويب
        self.import_web_view.page().setWebChannel(self.import_channel)
        
        # تحميل HTML مع إزالة أزرار الإغلاق
        import_html = self.get_import_html_modified()
        self.import_web_view.setHtml(import_html)
        
        self.tab_widget.addTab(self.import_web_view, "📥 الاستيراد")
        print("✅ تم إنشاء تبويبة الاستيراد مع الكائنات الأصلية")

        # 3. تبويبة بيانات المؤسسة - QWebEngineView مع نافذة بيانات المؤسسة الفعلية
        print("🏢 إنشاء تبويبة بيانات المؤسسة...")
        
        if self.institution_engine is None:
            print("⚠️ محرك بيانات المؤسسة غير متاح - إنشاء تبويبة احتياطية")
            placeholder_widget = self.create_placeholder_widget("🏢 بيانات المؤسسة", "محرك بيانات المؤسسة غير متاح")
            self.tab_widget.addTab(placeholder_widget, "🏢 بيانات المؤسسة")
        else:
            self.institution_web_view = QWebEngineView()
            
            # تحسين QWebEngineView للدمج المثالي - إضافة مسافة داخلية
            self.institution_web_view.setStyleSheet("""
                QWebEngineView {
                    border: none;
                    margin: 8px;
                    padding: 0px;
                    background: transparent;
                    border-radius: 8px;
                }
            """)
            
            # إعداد قناة التواصل لبيانات المؤسسة
            self.institution_channel = QWebChannel()
            self.institution_channel.registerObject("institutionEngine", self.institution_engine)
            
            # إنشاء كائن وهمي لإغلاق النافذة (معطل في الدمج)
            class InstitutionWindowProxy(QObject):
                @pyqtSlot()
                def closeWindow(self):
                    print("🚫 تم استدعاء closeWindow - معطلة في الوضع المدمج")
                    pass
            
            self.institution_window_proxy = InstitutionWindowProxy()
            self.institution_channel.registerObject("institutionWindow", self.institution_window_proxy)
            
            # ربط القناة بصفحة الويب
            self.institution_web_view.page().setWebChannel(self.institution_channel)
            
            # تحميل HTML مع إزالة أزرار الإغلاق
            institution_html = self.get_institution_html_modified()
            self.institution_web_view.setHtml(institution_html)
            
            self.tab_widget.addTab(self.institution_web_view, "🏢 بيانات المؤسسة")
            print("✅ تم إنشاء تبويبة بيانات المؤسسة مع الكائنات الأصلية")

        # 4. باقي التبويبات (تجريبية)
        tabs_data = [
            ("👥 إدارة الطلاب", "قوائم وبيانات الطلاب"),
            ("👨‍🏫 إدارة المعلمين", "بيانات الأساتذة والمعلمين"),
            ("📚 المناهج والمقررات", "إدارة المواد الدراسية"),
            ("📊 التقارير والإحصائيات", "تقارير شاملة ومفصلة"),
            ("🖨️ الطباعة", "إعدادات وقوالب الطباعة"),
            ("⚙️ الإعدادات", "تكوين النظام العام"),
            ("ℹ️ المساعدة والدعم", "أدلة الاستخدام والمساعدة")
        ]

        for title, description in tabs_data:
            widget = self.create_placeholder_widget(title, description)
            self.tab_widget.addTab(widget, title)

    def create_dashboard_widget(self):
        """إنشاء واجهة لوحة التحكم"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان رئيسي
        title_label = QLabel("🎓 مرحباً بك في النظام التعليمي الشامل")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin: 20px;
            padding: 20px;
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                       stop: 0 #667eea, stop: 1 #764ba2);
            color: white;
            border-radius: 15px;
        """)
        layout.addWidget(title_label)

        # معلومات النظام
        info_text = QTextEdit()
        info_text.setReadOnly(True)
        info_text.setHtml("""
        <div style="font-family: 'Segoe UI'; font-size: 14px; line-height: 1.6; padding: 10px;">
            <h2 style="color: #667eea; margin-bottom: 15px;">📋 نظرة عامة على النظام</h2>
            
            <p><strong>🏷️ الإصدار:</strong> 4.2 - QTabWidget مع دمج الاستيراد الحقيقي</p>
            <p><strong>📅 تاريخ الإصدار:</strong> يونيو 2025</p>
            <p><strong>🐍 إصدار Python:</strong> {}</p>
            
            <h3 style="color: #764ba2; margin-top: 20px; margin-bottom: 10px;">✨ الميزات الجديدة:</h3>
            <ul>
                <li>🎯 <strong>QTabWidget حقيقي:</strong> استبدال QStackedWidget بـ QTabWidget احترافي</li>
                <li>📥 <strong>دمج الاستيراد الحقيقي:</strong> نافذة الاستيراد مدمجة كتبويبة حقيقية</li>
                <li>🖼️ <strong>واجهة محسنة:</strong> تصميم Material Design مع تأثيرات حديثة</li>
                <li>⚡ <strong>أداء محسن:</strong> تحميل أسرع وذاكرة أقل</li>
                <li>🔄 <strong>تبديل سلس:</strong> انتقال سريع بين التبويبات</li>
            </ul>
            
            <h3 style="color: #764ba2; margin-top: 20px; margin-bottom: 10px;">📋 التبويبات المتاحة:</h3>
            <ul>
                <li>📊 <strong>لوحة التحكم:</strong> نظرة عامة على النظام (التبويبة الحالية)</li>
                <li>📥 <strong>الاستيراد:</strong> نافذة الاستيراد الفعلية من sub1_window_html.py</li>
                <li>🏢 <strong>بيانات المؤسسة:</strong> نافذة بيانات المؤسسة الفعلية من sub2_window_html.py</li>
                <li>👥 <strong>إدارة الطلاب:</strong> قوائم وبيانات الطلاب</li>
                <li>👨‍🏫 <strong>إدارة المعلمين:</strong> بيانات الأساتذة والمعلمين</li>
                <li>📚 <strong>المناهج والمقررات:</strong> إدارة المواد الدراسية</li>
                <li>📊 <strong>التقارير والإحصائيات:</strong> تقارير شاملة ومفصلة</li>
                <li>🖨️ <strong>الطباعة:</strong> إعدادات وقوالب الطباعة</li>
                <li>⚙️ <strong>الإعدادات:</strong> تكوين النظام العام</li>
                <li>ℹ️ <strong>المساعدة والدعم:</strong> أدلة الاستخدام والمساعدة</li>
            </ul>
            
            <div style="background: #e8f4f8; padding: 15px; border-radius: 10px; margin-top: 20px; border-left: 4px solid #667eea;">
                <p><strong>💡 تلميح:</strong> انقر على تبويبة "الاستيراد" أو "بيانات المؤسسة" لرؤية النوافذ الفعلية مدمجة داخل التبويبات!</p>
                <p><strong>🎯 جديد:</strong> تم دمج نافذة بيانات المؤسسة (sub2_window_html.py) بنفس منهجية الاستيراد</p>
            </div>
        </div>
        """.format(f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"))
        
        info_text.setStyleSheet("""
            background-color: white;
            border: 2px solid #e0e6ed;
            border-radius: 10px;
            padding: 10px;
        """)
        layout.addWidget(info_text)

        return widget

    def create_placeholder_widget(self, title, description):
        """إنشاء واجهة تجريبية للتبويبات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(40, 40, 40, 40)

        # عنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            margin: 20px;
            padding: 15px;
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                       stop: 0 #a8e6cf, stop: 1 #88d8a3);
            border-radius: 10px;
        """)
        layout.addWidget(title_label)

        # وصف
        desc_label = QLabel(f"📝 {description}")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("""
            font-size: 16px;
            color: #7f8c8d;
            margin: 10px;
            padding: 20px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
        """)
        layout.addWidget(desc_label)

        # رسالة مؤقتة
        temp_label = QLabel("🚧 هذه التبويبة قيد التطوير\nسيتم إضافة المحتوى قريباً")
        temp_label.setAlignment(Qt.AlignCenter)
        temp_label.setStyleSheet("""
            font-size: 14px;
            color: #e67e22;
            margin: 20px;
            padding: 20px;
            background: #fef9e7;
            border: 2px solid #f39c12;
            border-radius: 10px;
        """)
        layout.addWidget(temp_label)

        layout.addStretch()
        return widget

    def setup_web_channels(self):
        """إعداد قنوات التواصل مع JavaScript للاستيراد"""
        
        # قناة واجهة الاستيراد
        self.import_channel = QWebChannel()
        self.import_channel.registerObject("importEngine", self.import_engine)
        self.import_web_view.page().setWebChannel(self.import_channel)

        # ربط الإشارات
        self.import_web_view.loadFinished.connect(self.on_import_page_loaded)

    def on_import_page_loaded(self):
        """استدعاء عند انتهاء تحميل صفحة الاستيراد"""
        print("✅ تم تحميل واجهة الاستيراد المدمجة في التبويبة بنجاح")

    def on_tab_changed(self, index):
        """معالج تغيير التبويبة"""
        tab_text = self.tab_widget.tabText(index)
        print(f"🔄 تم التبديل إلى التبويبة: {tab_text} (فهرس: {index})")
        
        # تحديث محرك النافذة
        if "الاستيراد" in tab_text:
            self.window_engine.current_tab = "import"
        elif "لوحة التحكم" in tab_text:
            self.window_engine.current_tab = "dashboard"
        elif "بيانات المؤسسة" in tab_text:
            self.window_engine.current_tab = "institution"
        else:
            self.window_engine.current_tab = f"tab_{index}"
        
        self.window_engine.emit_log(f"تم التبديل إلى: {tab_text}", "info")

    def get_import_html_modified(self):
        """الحصول على HTML واجهة الاستيراد مع تعديلات للدمج"""
        try:
            # استخدام محرك الاستيراد للحصول على HTML المدمج (بدون زر الإغلاق)
            html_content = self.import_engine.get_embedded_html_interface()
            
            # إضافة CSS إضافي للدمج التام - قص حواف النافذة المدمجة فقط
            additional_css = """
            <style>
            /* تحسينات للدمج التام في التبويبة - قص حواف النافذة المدمجة فقط */
            .close-button { display: none !important; }
            .header-content { justify-content: center !important; }
            
            /* إزالة المسافات الخارجية من النافذة المدمجة وإضافة مسافة داخلية */
            html, body { 
                margin: 0 !important; 
                padding: 8px !important; 
                border: none !important;
                background: transparent !important;
                height: calc(100% - 16px) !important;
                overflow: hidden !important;
                box-sizing: border-box !important;
            }
            
            /* جعل النافذة المدمجة منغمسة داخل الحاوية مع مسافة مناسبة */
            .container, .main-container { 
                margin: 0 !important; 
                padding: 15px !important;
                border: 1px solid #e0e6ed !important;
                border-radius: 8px !important;
                box-shadow: inset 0 2px 4px rgba(0,0,0,0.1) !important;
                background: #fafbfc !important;
                height: calc(100% - 30px) !important;
                max-width: none !important;
                box-sizing: border-box !important;
            }
            
            /* إزالة رؤوس النوافذ والعناوين البارزة */
            .window-header, .header, .title-bar {
                display: none !important;
            }
            
            /* تعديل العنوان الرئيسي ليتناسب مع التصميم المنغمس */
            h1, .main-title {
                margin: 0 0 15px 0 !important;
                padding: 12px !important;
                font-size: 18px !important;
                border-radius: 6px !important;
                background: linear-gradient(135deg, #667eea, #764ba2) !important;
                color: white !important;
                text-align: center !important;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
            }
            
            /* تحسين الكروت والصناديق داخل النافذة المنغمسة */
            .card, .box, .section, .import-section {
                border: 1px solid #d1d5db !important;
                border-radius: 6px !important;
                margin: 8px 0 !important;
                padding: 12px !important;
                background: white !important;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
            }
            
            /* تحسين الأزرار داخل النافذة المنغمسة */
            .btn, button, .action-button {
                border-radius: 6px !important;
                margin: 4px !important;
                padding: 8px 16px !important;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
                transition: all 0.2s ease !important;
            }
            
            .btn:hover, button:hover, .action-button:hover {
                transform: translateY(-1px) !important;
                box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
            }
            
            /* تحسين المسافات للقوائم والجداول */
            .table-container, .list-container {
                margin: 8px 0 !important;
                padding: 10px !important;
                border-radius: 6px !important;
                background: white !important;
                border: 1px solid #e5e7eb !important;
            }
            
            /* إخفاء الفوتر إذا كان موجوداً */
            .footer, .bottom-bar {
                display: none !important;
            }
            
            /* تحسين الشريط الجانبي مع المسافات الجديدة */
            .sidebar {
                border-right: 1px solid #d1d5db !important;
                border-radius: 0 6px 6px 0 !important;
                padding: 8px !important;
                background: #f9fafb !important;
            }
            
            /* جعل المحتوى يتناسب مع التصميم المنغمس */
            .content, .main-content {
                height: calc(100% - 40px) !important;
                overflow-y: auto !important;
                padding: 8px !important;
                border-radius: 6px !important;
            }
            
            /* تحسين شريط التمرير */
            .content::-webkit-scrollbar, .main-content::-webkit-scrollbar {
                width: 8px !important;
            }
            
            .content::-webkit-scrollbar-track, .main-content::-webkit-scrollbar-track {
                background: #f1f5f9 !important;
                border-radius: 4px !important;
            }
            
            .content::-webkit-scrollbar-thumb, .main-content::-webkit-scrollbar-thumb {
                background: #cbd5e1 !important;
                border-radius: 4px !important;
            }
            
            .content::-webkit-scrollbar-thumb:hover, .main-content::-webkit-scrollbar-thumb:hover {
                background: #94a3b8 !important;
            }
            </style>
            """
            
            modified_html = modified_html.replace('</head>', additional_css + '</head>')
            
            print("✅ تم تحميل HTML واجهة الاستيراد الأصلية وتعديلها للدمج")
            return modified_html
            
        except Exception as e:
            print(f"❌ خطأ في تحميل واجهة الاستيراد الأصلية: {e}")
            return self.get_fallback_import_html()

    def get_import_html(self):
        """الحصول على HTML واجهة الاستيراد"""
        try:
            # استخدام محرك الاستيراد للحصول على HTML الفعلي
            html_content = self.import_engine.get_html_interface()
            print("✅ تم تحميل HTML واجهة الاستيراد من sub1_window_html.py")
            return html_content
        except Exception as e:
            print(f"❌ خطأ في تحميل واجهة الاستيراد: {e}")
            return self.get_fallback_import_html()

    def get_fallback_import_html(self):
        """HTML احتياطي لواجهة الاستيراد في حالة فشل التحميل"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <title>نظام الاستيراد</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            background: transparent;
            margin: 0;
            padding: 8px;
            min-height: calc(100vh - 16px);
            direction: rtl;
            overflow-x: hidden;
            box-sizing: border-box;
        }
        .container {
            background: #fafbfc;
            border-radius: 8px;
            padding: 15px;
            margin: 0;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #e0e6ed;
            height: calc(100% - 30px);
            box-sizing: border-box;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2em;
        }
        .error-message {
            background: #ffe6e6;
            color: #d63031;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #d63031;
            text-align: center;
            margin: 20px 0;
        }
        .info-message {
            background: #e8f4f8;
            color: #2980b9;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            text-align: center;
            margin: 20px 0;
        }
        .action-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📥 نظام الاستيراد المدمج</h1>
        
        <div class="info-message">
            🎯 <strong>تم دمج نافذة الاستيراد بنجاح!</strong><br>
            هذه هي تبويبة الاستيراد المدمجة داخل QTabWidget
        </div>
        
        <div class="error-message">
            ⚠️ تعذر تحميل واجهة الاستيراد الأصلية<br>
            يرجى التأكد من وجود ملف sub1_window_html.py وصحة محرك DataImportEngine
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <h3 style="color: #2c3e50; margin-bottom: 15px;">💡 معلومات التبويبة:</h3>
            <ul style="text-align: right; max-width: 400px; margin: 0 auto;">
                <li>📋 هذه تبويبة حقيقية داخل QTabWidget</li>
                <li>🔗 مرتبطة بمحرك الاستيراد DataImportEngine</li>
                <li>🌐 تستخدم QWebEngineView لعرض واجهة HTML</li>
                <li>⚡ تعمل بكامل وظائف نافذة الاستيراد الأصلية</li>
            </ul>
        </div>
        
        <center>
            <button class="action-button" onclick="testImport()">🧪 اختبار الاستيراد</button>
            <button class="action-button" onclick="showInfo()">ℹ️ معلومات إضافية</button>
        </center>
    </div>
    
    <script>
        let importEngine = null;
        
        // الانتظار حتى تصبح قناة التواصل جاهزة
        new QWebChannel(qt.webChannelTransport, function(channel) {
            importEngine = channel.objects.importEngine;
            console.log("✅ تم ربط محرك الاستيراد بنجاح");
            
            if (importEngine) {
                console.log("🔗 محرك الاستيراد متاح ومرتبط");
            }
        });
        
        function testImport() {
            if (importEngine) {
                console.log("🧪 اختبار محرك الاستيراد...");
                // يمكن إضافة استدعاءات للمحرك هنا
            } else {
                alert("⚠️ محرك الاستيراد غير متاح");
            }
        }
        
        function showInfo() {
            alert(`📋 معلومات تبويبة الاستيراد:
            
🎯 هذه تبويبة حقيقية مدمجة في QTabWidget
🔗 مرتبطة بمحرك DataImportEngine من sub1_window_html.py
🌐 تستخدم QWebEngineView + QWebChannel
⚡ تعمل بكامل وظائف نافذة الاستيراد الأصلية
            
✨ الدمج ناجح والتبويبة تعمل بشكل صحيح!`);
        }
    </script>
</body>
</html>"""

    def get_institution_html_modified(self):
        """الحصول على HTML واجهة بيانات المؤسسة مع تعديلات للدمج"""
        try:
            print("🔄 جاري تحميل HTML من محرك بيانات المؤسسة...")
            
            # التحقق من وجود المحرك
            if not hasattr(self, 'institution_engine') or self.institution_engine is None:
                print("❌ محرك بيانات المؤسسة غير موجود")
                raise Exception("محرك بيانات المؤسسة غير مهيأ")
            
            print(f"🟢 نوع المحرك: {type(self.institution_engine)}")
            
            # التحقق من وجود الدالة
            if not hasattr(self.institution_engine, 'get_complete_html'):
                print("❌ دالة get_complete_html غير موجودة في المحرك")
                available_methods = [method for method in dir(self.institution_engine) if not method.startswith('_')]
                print(f"🟡 الدوال المتاحة: {available_methods}")
                raise Exception("دالة get_complete_html غير متاحة")
            
            # استخدام محرك بيانات المؤسسة للحصول على HTML الفعلي
            print("🌐 استدعاء get_complete_html...")
            html_content = self.institution_engine.get_complete_html()
            print(f"🟢 نتيجة get_complete_html: {type(html_content)} | طول: {len(html_content) if html_content else 0}")
            
            if not html_content or len(html_content) < 100:
                print("❌ HTML المرجع فارغ أو صغير جداً")
                raise Exception("HTML المرجع غير صالح")
            
            print(f"✅ تم تحميل HTML بنجاح ({len(html_content)} حرف)")
            
            # تعديل HTML لإزالة أزرار الإغلاق وتحسين العرض للدمج
            modified_html = html_content.replace(
                '<button class="close-button" onclick="closeWindow()">✖</button>',
                '<!-- زر الإغلاق محذوف للدمج -->'
            )
            
            # إضافة CSS إضافي للدمج التام - قص حواف النافذة المدمجة فقط
            additional_css = """
            <style>
            /* تحسينات للدمج التام في التبويبة - قص حواف النافذة المدمجة فقط */
            .close-button { display: none !important; }
            .header-content { justify-content: center !important; }
            
            /* إزالة المسافات الخارجية من النافذة المدمجة وإضافة مسافة داخلية */
            html, body { 
                margin: 0 !important; 
                padding: 8px !important; 
                border: none !important;
                background: transparent !important;
                height: calc(100% - 16px) !important;
                overflow: hidden !important;
                box-sizing: border-box !important;
            }
            
            /* جعل النافذة المدمجة منغمسة داخل الحاوية مع مسافة مناسبة */
            .container, .main-container { 
                margin: 0 !important; 
                padding: 15px !important;
                border: 1px solid #e0e6ed !important;
                border-radius: 8px !important;
                box-shadow: inset 0 2px 4px rgba(0,0,0,0.1) !important;
                background: #fafbfc !important;
                height: calc(100% - 30px) !important;
                max-width: none !important;
                box-sizing: border-box !important;
            }
            
            /* إزالة رؤوس النوافذ والعناوين البارزة */
            .window-header, .header, .title-bar {
                display: none !important;
            }
            
            /* تعديل العنوان الرئيسي ليتناسب مع التصميم المنغمس */
            h1, .main-title {
                margin: 0 0 15px 0 !important;
                padding: 12px !important;
                font-size: 18px !important;
                border-radius: 6px !important;
                background: linear-gradient(135deg, #27ae60, #2ecc71) !important;
                color: white !important;
                text-align: center !important;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
            }
            
            /* تحسين الكروت والصناديق داخل النافذة المنغمسة */
            .card, .box, .section, .form-section, .logo-header {
                border: 1px solid #d1d5db !important;
                border-radius: 6px !important;
                margin: 8px 0 !important;
                padding: 12px !important;
                background: white !important;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
            }
            
            /* تحسين الأزرار داخل النافذة المنغمسة */
            .btn, button, .action-button {
                border-radius: 6px !important;
                margin: 4px !important;
                padding: 8px 16px !important;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
                transition: all 0.2s ease !important;
            }
            
            .btn:hover, button:hover, .action-button:hover {
                transform: translateY(-1px) !important;
                box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
            }
            
            /* تحسين النماذج والحقول */
            .form-group {
                margin: 8px 0 !important;
                padding: 8px !important;
            }
            
            .form-control, input, textarea, select {
                border: 1px solid #d1d5db !important;
                border-radius: 4px !important;
                padding: 8px !important;
                font-size: 14px !important;
            }
            
            .form-control:focus, input:focus, textarea:focus, select:focus {
                border-color: #27ae60 !important;
                box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.2) !important;
                outline: none !important;
            }
            
            /* تحسين عرض الشعار */
            .logo-display, .logo-placeholder {
                border: 2px dashed #27ae60 !important;
                border-radius: 6px !important;
                padding: 15px !important;
                text-align: center !important;
                background: #f8fffe !important;
            }
            
            /* تحسين الشريط الجانبي مع المسافات الجديدة */
            .sidebar {
                border-right: 1px solid #d1d5db !important;
                border-radius: 0 6px 6px 0 !important;
                padding: 8px !important;
                background: #f9fafb !important;
            }
            
            /* جعل المحتوى يتناسب مع التصميم المنغمس */
            .content, .main-content {
                height: calc(100% - 40px) !important;
                overflow-y: auto !important;
                padding: 8px !important;
                border-radius: 6px !important;
            }
            
            /* إخفاء الفوتر إذا كان موجوداً */
            .footer, .bottom-bar {
                display: none !important;
            }
            
            /* تحسين شريط التمرير */
            .content::-webkit-scrollbar, .main-content::-webkit-scrollbar {
                width: 8px !important;
            }
            
            .content::-webkit-scrollbar-track, .main-content::-webkit-scrollbar-track {
                background: #f1f5f9 !important;
                border-radius: 4px !important;
            }
            
            .content::-webkit-scrollbar-thumb, .main-content::-webkit-scrollbar-thumb {
                background: #cbd5e1 !important;
                border-radius: 4px !important;
            }
            
            .content::-webkit-scrollbar-thumb:hover, .main-content::-webkit-scrollbar-thumb:hover {
                background: #94a3b8 !important;
            }
            
            /* تحسين الجدول إذا كان موجوداً */
            table, .table {
                border-collapse: collapse !important;
                width: 100% !important;
                margin: 10px 0 !important;
                border: 1px solid #e5e7eb !important;
                border-radius: 6px !important;
                overflow: hidden !important;
            }
            
            th, td {
                padding: 8px 12px !important;
                border-bottom: 1px solid #f3f4f6 !important;
                text-align: right !important;
            }
            
            th {
                background: #f9fafb !important;
                font-weight: 600 !important;
                color: #374151 !important;
            }
            
            /* تحسين الرسائل والتنبيهات */
            .alert, .message, .notification {
                border-radius: 6px !important;
                margin: 8px 0 !important;
                padding: 12px !important;
                border-left: 4px solid !important;
            }
            
            .alert-success { border-left-color: #27ae60 !important; background: #f0fff4 !important; }
            .alert-warning { border-left-color: #f39c12 !important; background: #fffbf0 !important; }
            .alert-error { border-left-color: #e74c3c !important; background: #fff5f5 !important; }
            .alert-info { border-left-color: #3498db !important; background: #f0f9ff !important; }
            </style>
            """
            
            modified_html = modified_html.replace('</head>', additional_css + '</head>')
            
            print("✅ تم تحميل HTML واجهة بيانات المؤسسة الأصلية وتعديلها للدمج")
            return modified_html
            
        except Exception as e:
            print(f"❌ خطأ في تحميل واجهة بيانات المؤسسة الأصلية: {e}")
            return self.get_fallback_institution_html()

    def get_fallback_institution_html(self):
        """HTML احتياطي لواجهة بيانات المؤسسة في حالة فشل التحميل"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <title>بيانات المؤسسة</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            background: transparent;
            margin: 0;
            padding: 8px;
            min-height: calc(100vh - 16px);
            direction: rtl;
            overflow-x: hidden;
            box-sizing: border-box;
        }
        .container {
            background: #fafbfc;
            border-radius: 8px;
            padding: 15px;
            margin: 0;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #e0e6ed;
            height: calc(100% - 30px);
            box-sizing: border-box;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2em;
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 15px;
            border-radius: 8px;
        }
        .error-message {
            background: #ffe6e6;
            color: #d63031;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #d63031;
            text-align: center;
            margin: 20px 0;
        }
        .info-message {
            background: #e8f5e8;
            color: #27ae60;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #27ae60;
            text-align: center;
            margin: 20px 0;
        }
        .action-button {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏢 بيانات المؤسسة التعليمية</h1>
        
        <div class="info-message">
            🎯 <strong>تم دمج نافذة بيانات المؤسسة بنجاح!</strong><br>
            هذه هي تبويبة بيانات المؤسسة المدمجة داخل QTabWidget
        </div>
        
        <div class="error-message">
            ⚠️ تعذر تحميل واجهة بيانات المؤسسة الأصلية<br>
            يرجى التأكد من وجود ملف sub2_window_html.py وصحة محرك InstitutionEngine
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <h3 style="color: #2c3e50; margin-bottom: 15px;">💡 معلومات التبويبة:</h3>
            <ul style="text-align: right; max-width: 400px; margin: 0 auto;">
                <li>📋 هذه تبويبة حقيقية داخل QTabWidget</li>
                <li>🔗 مرتبطة بمحرك بيانات المؤسسة InstitutionEngine</li>
                <li>🌐 تستخدم QWebEngineView لعرض واجهة HTML</li>
                <li>⚡ تعمل بكامل وظائف نافذة بيانات المؤسسة الأصلية</li>
            </ul>
        </div>
        
        <center>
            <button class="action-button" onclick="testInstitution()">🧪 اختبار البيانات</button>
            <button class="action-button" onclick="showInfo()">ℹ️ معلومات إضافية</button>
        </center>
    </div>
    
    <script>
        let institutionEngine = null;
        
        // الانتظار حتى تصبح قناة التواصل جاهزة
        new QWebChannel(qt.webChannelTransport, function(channel) {
            institutionEngine = channel.objects.institutionEngine;
            console.log("✅ تم ربط محرك بيانات المؤسسة بنجاح");
            
            if (institutionEngine) {
                console.log("🔗 محرك بيانات المؤسسة متاح ومرتبط");
            }
        });
        
        function testInstitution() {
            if (institutionEngine) {
                console.log("🧪 اختبار محرك بيانات المؤسسة...");
                // يمكن إضافة استدعاءات للمحرك هنا
            } else {
                alert("⚠️ محرك بيانات المؤسسة غير متاح");
            }
        }
        
        function showInfo() {
            alert(`📋 معلومات تبويبة بيانات المؤسسة:
            
🎯 هذه تبويبة حقيقية مدمجة في QTabWidget
🔗 مرتبطة بمحرك InstitutionEngine من sub2_window_html.py
🌐 تستخدم QWebEngineView + QWebChannel
⚡ تعمل بكامل وظائف نافذة بيانات المؤسسة الأصلية
            
✨ الدمج ناجح والتبويبة تعمل بشكل صحيح!`);
        }
    </script>
</body>
</html>"""

def main():
    """تشغيل التطبيق"""
    app = QApplication(sys.argv)
    
    # إعداد الخط
    font = QFont("Segoe UI", 9)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = MainWindow()
    window.show()
    
    print("🚀 تم تشغيل النظام التعليمي الشامل - QTabWidget مع دمج النوافذ الحقيقية")
    print("📋 عدد التبويبات: 10")
    print("📥 تبويبة الاستيراد: مدمجة من sub1_window_html.py")
    print("🏢 تبويبة بيانات المؤسسة: مدمجة من sub2_window_html.py")
    print("🎯 النافذة: تعمل في وضع كامل الشاشة")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
